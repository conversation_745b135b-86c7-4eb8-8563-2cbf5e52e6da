{"@@locale": "pt", "@@last_modified": "2025-01-17T00:00:00.000Z", "appName": "Le<PERSON><PERSON>", "tagline": "Seu Assistente de Medidor Pré-pago", "splashQuote": "Não sou barato—sou consciente dos quilowatts.", "checkingPermissions": "Verificando permissões...", "initializing": "Inicializando...", "welcomeTitle": "<PERSON><PERSON>-vindo ao <PERSON>", "welcomeSubtitle": "Seu assistente pessoal de medidor pré-pago", "trackUsage": "Acompanhe Seu Uso", "getAlerts": "Receba Alertas Oportunos", "viewHistory": "<PERSON><PERSON>", "calculateCosts": "Calcular Custos", "trackUsageDesc": "Monitore seu consumo e gastos de eletricidade", "getAlertsDesc": "Receba notificações quando seu saldo estiver baixo", "viewHistoryDesc": "Veja suas leituras de medidor e recargas anteriores", "calculateCostsDesc": "Estime seus custos de eletricidade em diferentes períodos", "getStarted": "<PERSON><PERSON><PERSON>", "restoreData": "Restaurar Dados Anteriores", "restoreHelper": "Tem um backup de outro dispositivo?", "restoreDataTitle": "<PERSON><PERSON><PERSON>", "restoreDataContent": "Este recurso permitirá restaurar dados de um arquivo de backup.", "cancel": "<PERSON><PERSON><PERSON>", "chooseFile": "Escolher <PERSON>", "regionSettings": "Configurações Regionais", "language": "Idioma", "currency": "<PERSON><PERSON>", "selectLanguage": "Selecione seu idioma preferido para a interface do aplicativo.", "selectCurrency": "Selecione a moeda para suas leituras de medidor.", "currencyTip": "Dica: Selecione a moeda que corresponde às suas contas de eletricidade.", "perDay": "/dia", "dashboard": "<PERSON><PERSON>", "history": "Hist<PERSON><PERSON><PERSON>", "settings": "Configurações", "noEntriesFound": "Nenhuma entrada encontrada", "tryAdjustingFilters": "Tente ajustar seus filtros para ver mais entradas", "noEntriesYet": "Ainda não há entradas", "addFirstEntry": "Adicione sua primeira leitura de medidor ou recarga para começar", "errorLoadingData": "Erro ao carregar dados", "errorLoadingPreferences": "Erro ao carregar preferências: {error}", "meterReading": "Leitura do Medidor", "topUp": "Recarga", "lastUpdated": "Última Atualização", "daysRemaining": "<PERSON><PERSON> Restantes", "currentBalance": "<PERSON><PERSON>", "usageStatistics": "Estatísticas de Uso", "recentAverage": "<PERSON><PERSON><PERSON>", "totalAverage": "Média Total", "dailyUsage": "<PERSON><PERSON>", "topUpStatistics": "Estatísticas de Recarga", "daysToAlert": "Dias para Alerta", "daysToZero": "Dias até Zero", "quickActions": "Ações <PERSON>", "addEntry": "Adicionar <PERSON>", "recentActivity": "Atividade Recente", "viewAll": "<PERSON><PERSON>", "save": "<PERSON><PERSON>", "delete": "Excluir", "edit": "<PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "ok": "OK", "yes": "<PERSON>m", "no": "Não", "loading": "Carregando...", "saving": "Salvando...", "region": "Região", "languageCurrency": "Idioma, Moeda", "recentAvgUsage": "A média recente mostra o uso entre leituras consecutivas", "tapNotificationBell": "Toque no ícone do sino de notificação para ver todas as notificações", "addReadingsRegularly": "Adicione novas leituras do medidor regularmente para melhores estatísticas de uso", "setupAlertsLowBalance": "Configure alertas para ser notificado quando o saldo estiver baixo", "useQuickActions": "Use as Ações Rápidas para adicionar novas leituras ou recargas", "viewHistoryTip": "Veja seu histórico para ver todas as leituras do medidor e recargas passadas", "notificationsGrouped": "As notificações são agrupadas por tipo para fácil organização", "swipeNotifications": "Deslize para a esquerda nas notificações para marcar como lidas, para a direita para excluir", "configureThresholds": "Configure os limites de notificação em Configurações > Alertas e Notificações", "lowBalanceHelp": "Os alertas de saldo baixo ajudam você a evitar ficar sem crédito", "daysInAdvanceTip": "Defina \\\"Dias de Antecedência\\\" para receber lembretes de recarga cedo", "today": "Hoje", "yesterday": "Ontem", "lastWeek": "Semana passada", "lastMonth": "<PERSON><PERSON><PERSON> passado", "never": "Nunca", "days": "dias", "day": "dia", "hours": "horas", "hour": "hora", "minutes": "minutos", "minute": "minuto", "retry": "Tentar Novamente", "skip": "<PERSON><PERSON>", "complete": "Completo", "failed": "Fal<PERSON>", "syncing": "Sincronizando...", "deleting": "Excluindo...", "noMeterReading": "Nenhuma leitura do medidor disponível", "addFirstReading": "Adicione sua primeira leitura", "nextTopUp": "Próxima recarga", "addReading": "Adicionar leitura", "addTopUp": "<PERSON><PERSON><PERSON><PERSON> recarga", "noRecentActivity": "Nenhuma atividade recente", "invalidEntry": "Entrada inválida", "missingData": "Dados ausentes", "dataInconsistency": "Inconsistência de dados", "validationError": "Erro de validação", "failedToSave": "<PERSON>alha ao salvar", "networkError": "Erro de rede", "permissionDenied": "Permissão negada", "fileNotFound": "Arquivo não encontrado", "invalidFileFormat": "Formato de arquivo inválido", "addEntryDialog": "Adicionar entrada", "editEntry": "Editar entrada", "deleteEntry": "Excluir entrada", "confirmDelete": "Confirmar exclusão", "exportData": "Exportar dados", "importData": "Importar dados", "settingsDialog": "Configurações", "about": "Sobre", "lowBalanceAlert": "<PERSON><PERSON>a de saldo baixo", "timeToTopUp": "<PERSON><PERSON> de <PERSON>", "meterReadingReminder": "Lembrete de leitura do medidor", "dataBackupReminder": "Lembrete de backup de dados", "alertsNotifications": "Alertas e notificações", "dateTime": "Data e hora", "theme": "<PERSON><PERSON>", "dataManagement": "Gerenciamento de dados", "appInformation": "Informações do aplicativo", "setup": "Configuração", "setupRegionSettings": "Configurações Regionais", "setupRegionSettingsDesc": "Configure as preferências de idioma e moeda.", "setupInitialMeterReading": "Leitura Inicial do Medidor", "setupInitialMeterReadingDesc": "Digite sua leitura atual do medidor para começar o acompanhamento.", "setupAlertSettings": "Configurações de Alerta", "setupAlertSettingsDesc": "Configure quando você quer receber alertas sobre o saldo do seu medidor.", "setupDateSettings": "Configurações de Data", "setupDateSettingsDesc": "Configure como as datas são exibidas no aplicativo.", "setupAppearance": "Aparência", "setupAppearanceDesc": "Personalize a aparência do aplicativo.", "finishSetup": "Finalizar <PERSON>figu<PERSON>", "setupFailed": "Falha na configuração: {error}", "pleaseCheckInputs": "Por favor, verifique suas entradas.", "dateSettingsTitle": "Configurações de Data", "dateSettingsDesc": "Escolha como as datas serão exibidas em todo o aplicativo.", "dateFormat": "Formato de Data", "alertThreshold": "Limite de Alerta", "alertThresholdDesc": "Você será notificado quando seu saldo ficar abaixo deste valor.", "daysInAdvance": "Dias de Antecedência", "daysInAdvanceDesc": "Quantos dias antes de ficar sem crédito enviar lembretes.", "initialMeterReadingOptional": "Isso é opcional. Você pode pular esta etapa e adicionar sua primeira leitura do medidor mais tarde.", "errorLoadingSettings": "Erro ao carregar configurações: {error}", "cost": "Custo", "costOfElectric": "Custo da Eletricidade", "notEnoughData": "Dados insuficientes", "recentAvg": "<PERSON><PERSON><PERSON>", "totalAvg": "Média Total", "splashQuote1": "E aí? Só economizando seus watts (e reais)!", "splashQuote2": "Mantenha a calma e siga a corrente.", "splashQuote3": "Ohm meu Deus, você é eficiente!", "splashQuote4": "Ler medidores: a única vez que ver números caindo é algo bom.", "splashQuote5": "Economizar dinheiro na eletricidade? Isso é dinheiro bem wattado!", "splashQuote6": "Não sou pão-duro—sou quilowatt-consciente.", "splashQuote7": "O evento favorito da sua carteira: contas mais baixas.", "splashQuote8": "Nova baixa na sua conta? Isso merece uma celebração medidora!", "splashQuote9": "Lekky: o único lugar onde números negativos te fazem feliz.", "splashQuote10": "Não seja um resistor—vá com a corrente!", "dismiss": "Descar<PERSON>", "@dismiss": {"description": "Rótulo do botão para descartar um diálogo ou ação"}, "@editEntry": {"description": "Rótulo do botão ou diálogo para editar uma entrada"}, "dismissGap": "Descartar lacuna", "@dismissGap": {"description": "Rótulo do botão para descartar uma lacuna de dados"}, "dismissEntry": "Descartar entrada", "@dismissEntry": {"description": "Rótulo do botão para descartar uma entrada"}, "whatThisDoes": "O que isso faz:", "@whatThisDoes": {"description": "Rótulo explicando a ação de um botão ou recurso"}, "noValidationIssues": "Nenhum problema de validação encontrado", "@noValidationIssues": {"description": "Mensagem indicando que nenhum erro de validação foi encontrado"}, "allDataValid": "Todos os seus dados são válidos e consistentes", "@allDataValid": {"description": "Mensagem confirmando que todos os dados são válidos e consistentes"}, "refresh": "<PERSON><PERSON><PERSON><PERSON>", "@refresh": {"description": "Rótulo do botão para atualizar dados"}, "entryNotFound": "Entrada não encontrada", "@entryNotFound": {"description": "Mensagem indicando que uma entrada não foi encontrada"}, "negativeValue": "Valor Negativo", "futureDate": "Data Futura", "chronologicalOrder": "Ordem Cronológica", "balanceInconsistency": "Inconsistência de Saldo", "duplicateEntry": "Entrada Duplicada", "missingEntry": "Entrada Ausente", "otherIssue": "Outro Problema", "dismissGapDescription": "<PERSON><PERSON> des<PERSON> a lacuna de {days} dias criando uma entrada de Lacuna de Registros no seu histórico.", "@dismissGapDescription": {"description": "Descrição de descartar uma lacuna de dados", "placeholders": {"days": {}}}, "dismissDuplicateDescription": "<PERSON><PERSON> descar<PERSON> a entrada duplicada marcando-a como ignorada. A entrada permanecerá no seu histórico, mas será excluída das verificações de validação.", "@dismissDuplicateDescription": {"description": "Descrição de descartar uma entrada duplicada"}, "dismissGenericDescription": "Isso descartará o problema de validação.", "@dismissGenericDescription": {"description": "Descrição genérica para descartar um problema de validação"}, "recordsGapDismissed": "Lacuna de registros descartada com sucesso", "@recordsGapDismissed": {"description": "Mensagem de sucesso para descartar uma lacuna de registros"}, "duplicateEntryDismissed": "Entrada duplicada descartada com sucesso", "@duplicateEntryDismissed": {"description": "Mensagem de sucesso para descartar uma entrada duplicada"}, "errorDismissingGap": "Erro ao descartar lacuna: {error}", "@errorDismissingGap": {"description": "Mensagem de erro ao falhar ao descartar uma lacuna", "placeholders": {"error": {}}}, "errorDismissingDuplicate": "Erro ao descartar entrada duplicada: {error}", "@errorDismissingDuplicate": {"description": "Mensagem de erro ao falhar ao descartar uma entrada duplicada", "placeholders": {"error": {}}}, "reminderServiceUnavailable": "Serviço de lembretes temporariamente indisponível", "@reminderServiceUnavailable": {"description": "Mensagem de erro para serviço de lembretes indisponível"}, "notificationPermissionRequired": "Permissão de notificação necessária para lembretes", "@notificationPermissionRequired": {"description": "Mensagem indicando que permissão de notificação é necessária"}, "unexpectedError": "Ocorreu um erro inesperado", "@unexpectedError": {"description": "Mensagem para um erro inesperado"}, "waitAndTryAgain": "Espere um momento e tente novamente", "@waitAndTryAgain": {"description": "Instrução para esperar e tentar novamente"}, "restartApp": "Reinicie o aplicativo", "@restartApp": {"description": "Instrução para reiniciar o aplicativo"}, "checkInternetConnection": "Verifique a conexão com a internet", "@checkInternetConnection": {"description": "Instrução para verificar conexão com internet"}, "grantNotificationPermission": "Conceda permissão de notificação nas configurações", "@grantNotificationPermission": {"description": "Instrução para conceder permissão de notificação"}, "enableBackgroundRefresh": "Habilite a atualização em segundo plano", "@enableBackgroundRefresh": {"description": "Instrução para habilitar atualização em segundo plano"}, "disableBatteryOptimization": "Desative a otimização de bateria para este aplicativo", "@disableBatteryOptimization": {"description": "Instrução para desativar otimização de bateria"}, "tryAgain": "Tentar novamente", "@tryAgain": {"description": "Rótulo de botão para tentar uma ação novamente"}, "restartIfPersists": "Reinicie o aplicativo se o problema persistir", "@restartIfPersists": {"description": "Instrução para reiniciar se o problema persistir"}, "fileOperationFailed": "Operação de arquivo falhou. Tente novamente.", "@fileOperationFailed": {"description": "Mensagem de erro para operação de arquivo falhada"}, "fieldError": "Campo: {field}", "@fieldError": {"description": "Mensagem de erro especificando um campo com problema", "placeholders": {"field": {}}}, "currentMeterReading": "Leitura Atual do Medidor", "enterCurrentMeterReading": "Digite sua leitura atual do medidor", "enterNumberOnMeter": "Digite o número mostrado no seu medidor de eletricidade", "howToReadMeter": "Como ler seu medidor:", "locatePrepaIdMeter": "Localize seu medidor de eletricidade pré-pago.", "pressDisplayButton": "Pressione o botão do display até ver a leitura atual.", "enterNumberInCurrency": "Digite o número mostrado nas suas unidades de moeda.", "lookForTotalValue": "Alguns medidores mostram múltiplos valores - procure pelo rotulado como \"Total\" ou similar.", "takePhotoTip": "Dica: Tire uma foto do seu medidor para referência futura.", "meterReadingValidationError": "A leitura inicial do medidor deve estar entre 0.00 e 999.99", "current": "Atual", "selectLanguageDescription": "Selecione seu idioma preferido para a interface do aplicativo."}