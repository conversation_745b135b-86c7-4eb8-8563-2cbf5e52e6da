import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/theme_mode.dart';
import '../../providers/theme_provider.dart' as theme_provider;

/// A widget for selecting theme mode with enhanced Riverpod integration
class ThemeSelector extends ConsumerWidget {
  /// Current theme mode
  final AppThemeMode currentThemeMode;

  /// Callback when theme mode changes
  final Function(AppThemeMode) onThemeModeChanged;

  /// Whether to show preview of theme changes
  final bool showPreview;

  /// Whether to show extended theme options
  final bool showExtendedOptions;

  /// Constructor
  const ThemeSelector({
    super.key,
    required this.currentThemeMode,
    required this.onThemeModeChanged,
    this.showPreview = false,
    this.showExtendedOptions = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final themeAsync = ref.watch(theme_provider.themeProvider);

    return themeAsync.when(
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(
        child: Column(
          children: [
            const Icon(Icons.error, color: Colors.red),
            Text('Error: $error'),
            ElevatedButton(
              onPressed: () => ref.refresh(theme_provider.themeProvider),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
      data: (themeState) => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Theme',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 8),
          if (showPreview) ...[
            _buildThemePreview(context, themeState.themeMode),
            const SizedBox(height: 16),
          ],
          _buildThemeOption(
            context,
            ref,
            themeState.themeMode,
            AppThemeMode.system,
            Icons.brightness_auto,
            'System Default',
            'Follow system theme settings',
          ),
          const SizedBox(height: 8),
          _buildThemeOption(
            context,
            ref,
            themeState.themeMode,
            AppThemeMode.light,
            Icons.brightness_high,
            'Light Mode',
            'Always use light theme',
          ),
          const SizedBox(height: 8),
          _buildThemeOption(
            context,
            ref,
            themeState.themeMode,
            AppThemeMode.dark,
            Icons.brightness_4,
            'Dark Mode',
            'Always use dark theme',
          ),
          if (showExtendedOptions) ...[
            const SizedBox(height: 16),
            _buildExtendedOptions(context, ref),
          ],
        ],
      ),
    );
  }

  Widget _buildThemeOption(
    BuildContext context,
    WidgetRef ref,
    AppThemeMode currentTheme,
    AppThemeMode themeMode,
    IconData icon,
    String title,
    String description,
  ) {
    return InkWell(
      onTap: () => _updateTheme(ref, themeMode, context),
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(
            color: currentTheme == themeMode
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).dividerColor,
            width: currentTheme == themeMode ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Radio<AppThemeMode>(
              value: themeMode,
              groupValue: currentTheme,
              onChanged: (value) => _updateTheme(ref, value, context),
            ),
            const SizedBox(width: 8),
            Icon(icon),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 12,
                      color: Theme.of(context).textTheme.bodySmall?.color,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Update theme with haptic feedback and error handling
  void _updateTheme(
      WidgetRef ref, AppThemeMode? mode, BuildContext context) async {
    if (mode == null) return;

    try {
      // Haptic feedback for theme change
      HapticFeedback.selectionClick();

      // Update theme through provider
      await ref
          .read(theme_provider.themeProvider.notifier)
          .updateThemeMode(mode);

      // Call legacy callback for backward compatibility
      onThemeModeChanged(mode);

      // Show success feedback
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Theme changed to ${mode.displayName}'),
            duration: const Duration(seconds: 1),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (error) {
      // Show error feedback
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to change theme: $error'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
            behavior: SnackBarBehavior.floating,
            action: SnackBarAction(
              label: 'Retry',
              textColor: Colors.white,
              onPressed: () => _updateTheme(ref, mode, context),
            ),
          ),
        );
      }
    }
  }

  Widget _buildThemePreview(BuildContext context, AppThemeMode currentTheme) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Theme.of(context).dividerColor),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            Icons.preview,
            color: Theme.of(context).colorScheme.primary,
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(
            'Current: ${currentTheme.displayName}',
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
          const Spacer(),
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExtendedOptions(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Advanced Options',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),
        ListTile(
          leading: const Icon(Icons.accessibility),
          title: const Text('High Contrast'),
          subtitle: const Text('Enhanced visibility for accessibility'),
          trailing: Switch(
            value: false,
            onChanged: (value) {
              // Future enhancement: implement high contrast mode
            },
          ),
          contentPadding: EdgeInsets.zero,
        ),
      ],
    );
  }
}
