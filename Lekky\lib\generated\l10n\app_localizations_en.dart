import 'app_localizations.dart';

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appName => 'Lekky';

  @override
  String get tagline => 'Your Prepaid Meter Assistant';

  @override
  String get splashQuote => 'I\'m not cheap—I\'m kilowatt-conscious.';

  @override
  String get checkingPermissions => 'Checking permissions...';

  @override
  String get initializing => 'Initializing...';

  @override
  String get welcomeTitle => 'Welcome to Lekky';

  @override
  String get welcomeSubtitle => 'Your personal prepaid meter assistant';

  @override
  String get trackUsage => 'Track Your Usage';

  @override
  String get getAlerts => 'Get Timely Alerts';

  @override
  String get viewHistory => 'View History';

  @override
  String get calculateCosts => 'Calculate Costs';

  @override
  String get trackUsageDesc => 'Monitor your electricity consumption and spending';

  @override
  String get getAlertsDesc => 'Receive notifications when your balance is running low';

  @override
  String get viewHistoryDesc => 'See your past meter readings and top-ups';

  @override
  String get calculateCostsDesc => 'Estimate your electricity costs over different periods';

  @override
  String get getStarted => 'Get Started';

  @override
  String get restoreData => 'Restore Previous Data';

  @override
  String get restoreHelper => 'Have a backup from another device?';

  @override
  String get restoreDataTitle => 'Restore Data';

  @override
  String get restoreDataContent => 'This feature will allow you to restore data from a backup file.';

  @override
  String get cancel => 'Cancel';

  @override
  String get chooseFile => 'Choose File';

  @override
  String get regionSettings => 'Region Settings';

  @override
  String get language => 'Language';

  @override
  String get currency => 'Currency';

  @override
  String get selectLanguage => 'Select your preferred language for the app interface.';

  @override
  String get selectCurrency => 'Select the currency for your meter readings.';

  @override
  String get currencyTip => 'Tip: Select the currency that matches your electricity bills.';

  @override
  String get perDay => '/day';

  @override
  String get dashboard => 'Dashboard';

  @override
  String get history => 'History';

  @override
  String get settings => 'Settings';

  @override
  String get noEntriesFound => 'No entries found';

  @override
  String get tryAdjustingFilters => 'Try adjusting your filters to see more entries';

  @override
  String get noEntriesYet => 'No entries yet';

  @override
  String get addFirstEntry => 'Add your first meter reading or top-up to get started';

  @override
  String get errorLoadingData => 'Error loading data';

  @override
  String errorLoadingPreferences(String error) {
    return 'Error loading preferences: $error';
  }

  @override
  String get meterReading => 'Meter Reading';

  @override
  String get topUp => 'Top Up';

  @override
  String get lastUpdated => 'Last Updated';

  @override
  String get daysRemaining => 'Days Remaining';

  @override
  String get currentBalance => 'Current Balance';

  @override
  String get usageStatistics => 'Usage Statistics';

  @override
  String get recentAverage => 'Recent Average';

  @override
  String get totalAverage => 'Total Average';

  @override
  String get dailyUsage => 'Daily Usage';

  @override
  String get topUpStatistics => 'Top Up Statistics';

  @override
  String get daysToAlert => 'Days to Alert';

  @override
  String get daysToZero => 'Days to Zero';

  @override
  String get quickActions => 'Quick Actions';

  @override
  String get addEntry => 'Add Entry';

  @override
  String get recentActivity => 'Recent Activity';

  @override
  String get viewAll => 'View All';

  @override
  String get save => 'Save';

  @override
  String get delete => 'Delete';

  @override
  String get edit => 'Edit';

  @override
  String get add => 'Add';

  @override
  String get close => 'Close';

  @override
  String get ok => 'OK';

  @override
  String get yes => 'Yes';

  @override
  String get no => 'No';

  @override
  String get loading => 'Loading...';

  @override
  String get saving => 'Saving...';

  @override
  String get region => 'Region';

  @override
  String get languageCurrency => 'Language, Currency';

  @override
  String get recentAvgUsage => 'Recent-avg shows usage between consecutive readings';

  @override
  String get tapNotificationBell => 'Tap the notification bell icon to view all notifications';

  @override
  String get addReadingsRegularly => 'Add new meter readings regularly for better usage statistics';

  @override
  String get setupAlertsLowBalance => 'Set up alerts to be notified when your balance is low';

  @override
  String get useQuickActions => 'Use the Quick Actions to add new readings or top-ups';

  @override
  String get viewHistoryTip => 'View your history to see all past meter readings and top-ups';

  @override
  String get notificationsGrouped => 'Notifications are grouped by type for easy organization';

  @override
  String get swipeNotifications => 'Swipe left on notifications to mark as read, right to delete';

  @override
  String get configureThresholds => 'Configure notification thresholds in Settings > Alerts & Notifications';

  @override
  String get lowBalanceHelp => 'Low balance alerts help you avoid running out of credit';

  @override
  String get daysInAdvanceTip => 'Set \\\"Days in Advance\\\" to get top-up reminders early';

  @override
  String get today => 'Today';

  @override
  String get yesterday => 'Yesterday';

  @override
  String get lastWeek => 'Last week';

  @override
  String get lastMonth => 'Last month';

  @override
  String get never => 'Never';

  @override
  String get days => 'days';

  @override
  String get day => 'day';

  @override
  String get hours => 'hours';

  @override
  String get hour => 'hour';

  @override
  String get minutes => 'minutes';

  @override
  String get minute => 'minute';

  @override
  String get retry => 'Retry';

  @override
  String get skip => 'Skip';

  @override
  String get complete => 'Complete';

  @override
  String get failed => 'Failed';

  @override
  String get syncing => 'Syncing...';

  @override
  String get deleting => 'Deleting...';

  @override
  String get noMeterReading => 'No meter reading available';

  @override
  String get addFirstReading => 'Add your first reading';

  @override
  String get nextTopUp => 'Next top-up';

  @override
  String get addReading => 'Add reading';

  @override
  String get addTopUp => 'Add top-up';

  @override
  String get noRecentActivity => 'No recent activity';

  @override
  String get invalidEntry => 'Invalid entry';

  @override
  String get missingData => 'Missing data';

  @override
  String get dataInconsistency => 'Data inconsistency';

  @override
  String get validationError => 'Validation error';

  @override
  String get failedToSave => 'Failed to save';

  @override
  String get networkError => 'Network error';

  @override
  String get permissionDenied => 'Permission denied';

  @override
  String get fileNotFound => 'File not found';

  @override
  String get invalidFileFormat => 'Invalid file format';

  @override
  String get addEntryDialog => 'Add Entry';

  @override
  String get editEntry => 'Edit Entry';

  @override
  String get deleteEntry => 'Delete Entry';

  @override
  String get confirmDelete => 'Confirm Delete';

  @override
  String get exportData => 'Export Data';

  @override
  String get importData => 'Import Data';

  @override
  String get settingsDialog => 'Settings';

  @override
  String get about => 'About';

  @override
  String get lowBalanceAlert => 'Low balance alert';

  @override
  String get timeToTopUp => 'Time to top up';

  @override
  String get meterReadingReminder => 'Meter reading reminder';

  @override
  String get dataBackupReminder => 'Data backup reminder';

  @override
  String get alertsNotifications => 'Alerts & Notifications';

  @override
  String get dateTime => 'Date & Time';

  @override
  String get theme => 'Theme';

  @override
  String get dataManagement => 'Data Management';

  @override
  String get appInformation => 'App Information';

  @override
  String get setup => 'Setup';

  @override
  String get setupRegionSettings => 'Region Settings';

  @override
  String get setupRegionSettingsDesc => 'Configure language and currency preferences.';

  @override
  String get setupInitialMeterReading => 'Initial Meter Reading';

  @override
  String get setupInitialMeterReadingDesc => 'Enter your current meter reading to start tracking.';

  @override
  String get setupAlertSettings => 'Alert Settings';

  @override
  String get setupAlertSettingsDesc => 'Configure when you want to receive alerts about your meter balance.';

  @override
  String get setupDateSettings => 'Date Settings';

  @override
  String get setupDateSettingsDesc => 'Configure how dates are displayed in the app.';

  @override
  String get setupAppearance => 'Appearance';

  @override
  String get setupAppearanceDesc => 'Customize the look and feel of the app.';

  @override
  String get finishSetup => 'Finish Setup';

  @override
  String setupFailed(String error) {
    return 'Setup failed: $error';
  }

  @override
  String get pleaseCheckInputs => 'Please check your inputs.';

  @override
  String get dateSettingsTitle => 'Date Settings';

  @override
  String get dateSettingsDesc => 'Choose how dates will be displayed throughout the app.';

  @override
  String get dateFormat => 'Date Format';

  @override
  String get alertThreshold => 'Alert Threshold';

  @override
  String get alertThresholdDesc => 'You will be notified when your balance falls below this amount.';

  @override
  String get daysInAdvance => 'Days in Advance';

  @override
  String get daysInAdvanceDesc => 'How many days before running out of credit to send reminders.';

  @override
  String get initialMeterReadingOptional => 'This is optional. You can skip this step and add your first meter reading later.';

  @override
  String errorLoadingSettings(String error) {
    return 'Error loading settings: $error';
  }

  @override
  String get cost => 'Cost';

  @override
  String get costOfElectric => 'Cost of Electric';

  @override
  String get notEnoughData => 'Not enough data';

  @override
  String get recentAvg => 'Recent Avg.';

  @override
  String get totalAvg => 'Total Avg.';

  @override
  String get splashQuote1 => 'Watt\'s up? Just saving you watts (and pounds)!';

  @override
  String get splashQuote2 => 'Keep calm and current on.';

  @override
  String get splashQuote3 => 'Ohm my gosh, you\'re efficient!';

  @override
  String get splashQuote4 => 'Reading meters: the only time watching numbers go down is a good thing.';

  @override
  String get splashQuote5 => 'Saving money on electricity? Now that\'s money well watts!';

  @override
  String get splashQuote6 => 'I\'m not cheap—I\'m kilowatt-conscious.';

  @override
  String get splashQuote7 => 'Your wallet\'s favorite current event: lower bills.';

  @override
  String get splashQuote8 => 'Hit a new low on your bill? That calls for a meter-y celebration!';

  @override
  String get splashQuote9 => 'Lekky: the only place where negative numbers make you happy.';

  @override
  String get splashQuote10 => 'Don\'t be a resistor—go with the flow!';

  @override
  String get dismiss => 'Dismiss';

  @override
  String get dismissGap => 'Dismiss Gap';

  @override
  String get dismissEntry => 'Dismiss Entry';

  @override
  String get whatThisDoes => 'What this does:';

  @override
  String get noValidationIssues => 'No validation issues found';

  @override
  String get allDataValid => 'All your data is valid and consistent';

  @override
  String get refresh => 'Refresh';

  @override
  String get entryNotFound => 'Entry not found';

  @override
  String get negativeValue => 'Negative Value';

  @override
  String get futureDate => 'Future Date';

  @override
  String get chronologicalOrder => 'Chronological Order';

  @override
  String get balanceInconsistency => 'Balance Inconsistency';

  @override
  String get duplicateEntry => 'Duplicate Entry';

  @override
  String get missingEntry => 'Missing Entry';

  @override
  String get otherIssue => 'Other Issue';

  @override
  String dismissGapDescription(Object days) {
    return 'This will dismiss the $days day gap by creating a Records Gap entry in your history.';
  }

  @override
  String get dismissDuplicateDescription => 'This will dismiss the duplicate entry by marking it as ignored. The entry will remain in your history but will be excluded from validation checks.';

  @override
  String get dismissGenericDescription => 'This will dismiss the validation issue.';

  @override
  String get recordsGapDismissed => 'Records gap successfully dismissed';

  @override
  String get duplicateEntryDismissed => 'Duplicate entry successfully dismissed';

  @override
  String errorDismissingGap(Object error) {
    return 'Error dismissing gap: $error';
  }

  @override
  String errorDismissingDuplicate(Object error) {
    return 'Error dismissing duplicate entry: $error';
  }

  @override
  String get reminderServiceUnavailable => 'Reminder service temporarily unavailable';

  @override
  String get notificationPermissionRequired => 'Notification permission required for reminders';

  @override
  String get unexpectedError => 'An unexpected error occurred';

  @override
  String get waitAndTryAgain => 'Wait a moment and try again';

  @override
  String get restartApp => 'Restart the app';

  @override
  String get checkInternetConnection => 'Check internet connection';

  @override
  String get grantNotificationPermission => 'Grant notification permission in settings';

  @override
  String get enableBackgroundRefresh => 'Enable background app refresh';

  @override
  String get disableBatteryOptimization => 'Disable battery optimization for this app';

  @override
  String get tryAgain => 'Try again';

  @override
  String get restartIfPersists => 'Restart the app if the issue persists';

  @override
  String get fileOperationFailed => 'File operation failed. Please try again.';

  @override
  String fieldError(Object field) {
    return 'Field: $field';
  }

  @override
  String get currentMeterReading => 'Current Meter Reading';

  @override
  String get enterCurrentMeterReading => 'Enter your current meter reading';

  @override
  String get enterNumberOnMeter => 'Enter the number shown on your electricity meter';

  @override
  String get howToReadMeter => 'How to read your meter:';

  @override
  String get locatePrepaIdMeter => 'Locate your prepaid electricity meter.';

  @override
  String get pressDisplayButton => 'Press the display button until you see the current reading.';

  @override
  String get enterNumberInCurrency => 'Enter the number shown in your currency units.';

  @override
  String get lookForTotalValue => 'Some meters show multiple values - look for the one labeled \"Total\" or similar.';

  @override
  String get takePhotoTip => 'Tip: Take a photo of your meter for future reference.';

  @override
  String get meterReadingValidationError => 'Initial meter reading must be between 0.00 and 999.99';

  @override
  String get current => 'Current';

  @override
  String get selectLanguageDescription => 'Select your preferred language for the app interface.';
}
