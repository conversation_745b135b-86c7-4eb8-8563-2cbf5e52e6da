import 'app_localizations.dart';

/// The translations for German (`de`).
class AppLocalizationsDe extends AppLocalizations {
  AppLocalizationsDe([String locale = 'de']) : super(locale);

  @override
  String get appName => 'Lekky';

  @override
  String get tagline => 'Ihr Prepaid-Zähler-Assistent';

  @override
  String get splashQuote => 'Ich bin nicht geizig—ich bin kilowatt-bewusst.';

  @override
  String get checkingPermissions => 'Berechtigungen werden überprüft...';

  @override
  String get initializing => 'Initialisierung...';

  @override
  String get welcomeTitle => 'Willkommen bei Lekky';

  @override
  String get welcomeSubtitle => 'Ihr persönlicher Prepaid-Zähler-Assistent';

  @override
  String get trackUsage => 'Verbrauch Verfolgen';

  @override
  String get getAlerts => 'Rechtzeitige Benachrichtigungen Erhalten';

  @override
  String get viewHistory => 'Verlauf <PERSON>';

  @override
  String get calculateCosts => 'Kosten Berechnen';

  @override
  String get trackUsageDesc => 'Überwachen Sie Ihren Stromverbrauch und Ihre Ausgaben';

  @override
  String get getAlertsDesc => 'Erhalten Sie Benachrichtigungen, wenn Ihr Guthaben niedrig ist';

  @override
  String get viewHistoryDesc => 'Sehen Sie Ihre vergangenen Zählerstände und Aufladungen';

  @override
  String get calculateCostsDesc => 'Schätzen Sie Ihre Stromkosten über verschiedene Zeiträume';

  @override
  String get getStarted => 'Loslegen';

  @override
  String get restoreData => 'Vorherige Daten Wiederherstellen';

  @override
  String get restoreHelper => 'Haben Sie ein Backup von einem anderen Gerät?';

  @override
  String get restoreDataTitle => 'Daten Wiederherstellen';

  @override
  String get restoreDataContent => 'Diese Funktion ermöglicht es Ihnen, Daten aus einer Backup-Datei wiederherzustellen.';

  @override
  String get cancel => 'Abbrechen';

  @override
  String get chooseFile => 'Datei Auswählen';

  @override
  String get regionSettings => 'Regionale Einstellungen';

  @override
  String get language => 'Sprache';

  @override
  String get currency => 'Währung';

  @override
  String get selectLanguage => 'Wählen Sie Ihre bevorzugte Sprache für die App-Oberfläche.';

  @override
  String get selectCurrency => 'Wählen Sie die Währung für Ihre Zählerstände.';

  @override
  String get currencyTip => 'Tipp: Wählen Sie die Währung, die Ihren Stromrechnungen entspricht.';

  @override
  String get perDay => '/Tag';

  @override
  String get dashboard => 'Dashboard';

  @override
  String get history => 'Verlauf';

  @override
  String get settings => 'Einstellungen';

  @override
  String get noEntriesFound => 'Keine Einträge gefunden';

  @override
  String get tryAdjustingFilters => 'Versuchen Sie, Ihre Filter anzupassen, um mehr Einträge zu sehen';

  @override
  String get noEntriesYet => 'Noch keine Einträge';

  @override
  String get addFirstEntry => 'Fügen Sie Ihren ersten Zählerstand oder Ihre erste Aufladung hinzu, um zu beginnen';

  @override
  String get errorLoadingData => 'Fehler beim Laden der Daten';

  @override
  String errorLoadingPreferences(String error) {
    return 'Fehler beim Laden der Einstellungen: $error';
  }

  @override
  String get meterReading => 'Zählerstand';

  @override
  String get topUp => 'Aufladung';

  @override
  String get lastUpdated => 'Zuletzt Aktualisiert';

  @override
  String get daysRemaining => 'Verbleibende Tage';

  @override
  String get currentBalance => 'Aktuelles Guthaben';

  @override
  String get usageStatistics => 'Verbrauchsstatistiken';

  @override
  String get recentAverage => 'Aktueller Durchschnitt';

  @override
  String get totalAverage => 'Gesamtdurchschnitt';

  @override
  String get dailyUsage => 'Täglicher Verbrauch';

  @override
  String get topUpStatistics => 'Aufladungsstatistiken';

  @override
  String get daysToAlert => 'Tage bis Warnung';

  @override
  String get daysToZero => 'Tage bis Null';

  @override
  String get quickActions => 'Schnellaktionen';

  @override
  String get addEntry => 'Eintrag Hinzufügen';

  @override
  String get recentActivity => 'Letzte Aktivität';

  @override
  String get viewAll => 'Alle Anzeigen';

  @override
  String get save => 'Speichern';

  @override
  String get delete => 'Löschen';

  @override
  String get edit => 'Bearbeiten';

  @override
  String get add => 'Hinzufügen';

  @override
  String get close => 'Schließen';

  @override
  String get ok => 'OK';

  @override
  String get yes => 'Ja';

  @override
  String get no => 'Nein';

  @override
  String get loading => 'Laden...';

  @override
  String get saving => 'Speichern...';

  @override
  String get region => 'Region';

  @override
  String get languageCurrency => 'Sprache, Währung';

  @override
  String get recentAvgUsage => 'Der aktuelle Durchschnitt zeigt den Verbrauch zwischen aufeinanderfolgenden Messungen';

  @override
  String get tapNotificationBell => 'Tippen Sie auf das Benachrichtigungsglocken-Symbol, um alle Benachrichtigungen anzuzeigen';

  @override
  String get addReadingsRegularly => 'Fügen Sie regelmäßig neue Zählerstände hinzu für bessere Verbrauchsstatistiken';

  @override
  String get setupAlertsLowBalance => 'Richten Sie Benachrichtigungen ein, um bei niedrigem Guthaben informiert zu werden';

  @override
  String get useQuickActions => 'Verwenden Sie die Schnellaktionen, um neue Zählerstände oder Aufladungen hinzuzufügen';

  @override
  String get viewHistoryTip => 'Sehen Sie Ihren Verlauf, um alle vergangenen Zählerstände und Aufladungen zu sehen';

  @override
  String get notificationsGrouped => 'Benachrichtigungen sind nach Typ gruppiert für einfache Organisation';

  @override
  String get swipeNotifications => 'Wischen Sie nach links bei Benachrichtigungen, um sie als gelesen zu markieren, nach rechts zum Löschen';

  @override
  String get configureThresholds => 'Konfigurieren Sie Benachrichtigungsschwellen in Einstellungen > Benachrichtigungen und Warnungen';

  @override
  String get lowBalanceHelp => 'Niedrige Guthaben-Warnungen helfen Ihnen, nicht ohne Guthaben dazustehen';

  @override
  String get daysInAdvanceTip => 'Stellen Sie \\\"Tage im Voraus\\\" ein, um Aufladungserinnerungen früh zu erhalten';

  @override
  String get today => 'Heute';

  @override
  String get yesterday => 'Gestern';

  @override
  String get lastWeek => 'Letzte Woche';

  @override
  String get lastMonth => 'Letzter Monat';

  @override
  String get never => 'Nie';

  @override
  String get days => 'Tage';

  @override
  String get day => 'Tag';

  @override
  String get hours => 'Stunden';

  @override
  String get hour => 'Stunde';

  @override
  String get minutes => 'Minuten';

  @override
  String get minute => 'Minute';

  @override
  String get retry => 'Wiederholen';

  @override
  String get skip => 'Überspringen';

  @override
  String get complete => 'Vollständig';

  @override
  String get failed => 'Fehlgeschlagen';

  @override
  String get syncing => 'Synchronisierung...';

  @override
  String get deleting => 'Löschen...';

  @override
  String get noMeterReading => 'Keine Zählerablesung verfügbar';

  @override
  String get addFirstReading => 'Fügen Sie Ihre erste Ablesung hinzu';

  @override
  String get nextTopUp => 'Nächste Aufladung';

  @override
  String get addReading => 'Ablesung hinzufügen';

  @override
  String get addTopUp => 'Aufladung hinzufügen';

  @override
  String get noRecentActivity => 'Keine aktuelle Aktivität';

  @override
  String get invalidEntry => 'Ungültiger Eintrag';

  @override
  String get missingData => 'Fehlende Daten';

  @override
  String get dataInconsistency => 'Dateninkonsistenz';

  @override
  String get validationError => 'Validierungsfehler';

  @override
  String get failedToSave => 'Speichern fehlgeschlagen';

  @override
  String get networkError => 'Netzwerkfehler';

  @override
  String get permissionDenied => 'Berechtigung verweigert';

  @override
  String get fileNotFound => 'Datei nicht gefunden';

  @override
  String get invalidFileFormat => 'Ungültiges Dateiformat';

  @override
  String get addEntryDialog => 'Eintrag hinzufügen';

  @override
  String get editEntry => 'Eintrag bearbeiten';

  @override
  String get deleteEntry => 'Eintrag löschen';

  @override
  String get confirmDelete => 'Löschen bestätigen';

  @override
  String get exportData => 'Daten exportieren';

  @override
  String get importData => 'Daten importieren';

  @override
  String get settingsDialog => 'Einstellungen';

  @override
  String get about => 'Über';

  @override
  String get lowBalanceAlert => 'Warnung bei niedrigem Guthaben';

  @override
  String get timeToTopUp => 'Zeit zum Aufladen';

  @override
  String get meterReadingReminder => 'Erinnerung an Zählerablesung';

  @override
  String get dataBackupReminder => 'Erinnerung an Datensicherung';

  @override
  String get alertsNotifications => 'Benachrichtigungen und Warnungen';

  @override
  String get dateTime => 'Datum und Uhrzeit';

  @override
  String get theme => 'Design';

  @override
  String get dataManagement => 'Datenverwaltung';

  @override
  String get appInformation => 'App-Informationen';

  @override
  String get setup => 'Einrichtung';

  @override
  String get setupRegionSettings => 'Regionale Einstellungen';

  @override
  String get setupRegionSettingsDesc => 'Sprach- und Währungseinstellungen konfigurieren.';

  @override
  String get setupInitialMeterReading => 'Anfänglicher Zählerstand';

  @override
  String get setupInitialMeterReadingDesc => 'Geben Sie Ihren aktuellen Zählerstand ein, um die Verfolgung zu starten.';

  @override
  String get setupAlertSettings => 'Benachrichtigungseinstellungen';

  @override
  String get setupAlertSettingsDesc => 'Konfigurieren Sie, wann Sie Benachrichtigungen über Ihr Zählerguthaben erhalten möchten.';

  @override
  String get setupDateSettings => 'Datumseinstellungen';

  @override
  String get setupDateSettingsDesc => 'Konfigurieren Sie, wie Daten in der App angezeigt werden.';

  @override
  String get setupAppearance => 'Erscheinungsbild';

  @override
  String get setupAppearanceDesc => 'Passen Sie das Aussehen der App an.';

  @override
  String get finishSetup => 'Einrichtung Abschließen';

  @override
  String setupFailed(String error) {
    return 'Einrichtung fehlgeschlagen: $error';
  }

  @override
  String get pleaseCheckInputs => 'Bitte überprüfen Sie Ihre Eingaben.';

  @override
  String get dateSettingsTitle => 'Datumseinstellungen';

  @override
  String get dateSettingsDesc => 'Wählen Sie, wie Daten in der gesamten App angezeigt werden.';

  @override
  String get dateFormat => 'Datumsformat';

  @override
  String get alertThreshold => 'Benachrichtigungsschwelle';

  @override
  String get alertThresholdDesc => 'Sie werden benachrichtigt, wenn Ihr Guthaben unter diesen Betrag fällt.';

  @override
  String get daysInAdvance => 'Tage im Voraus';

  @override
  String get daysInAdvanceDesc => 'Wie viele Tage vor dem Aufbrauchen des Guthabens Erinnerungen senden.';

  @override
  String get initialMeterReadingOptional => 'Dies ist optional. Sie können diesen Schritt überspringen und Ihren ersten Zählerstand später hinzufügen.';

  @override
  String errorLoadingSettings(String error) {
    return 'Fehler beim Laden der Einstellungen: $error';
  }

  @override
  String get cost => 'Kosten';

  @override
  String get costOfElectric => 'Stromkosten';

  @override
  String get notEnoughData => 'Nicht genügend Daten';

  @override
  String get recentAvg => 'Aktueller Durchschnitt';

  @override
  String get totalAvg => 'Gesamtdurchschnitt';

  @override
  String get splashQuote1 => 'Was ist los? Spare nur deine Watts (und Euro)!';

  @override
  String get splashQuote2 => 'Bleib ruhig und folge dem Strom.';

  @override
  String get splashQuote3 => 'Ohm mein Gott, du bist effizient!';

  @override
  String get splashQuote4 => 'Zähler ablesen: das einzige Mal, wo sinkende Zahlen gut sind.';

  @override
  String get splashQuote5 => 'Geld bei Strom sparen? Das ist gut gewattetes Geld!';

  @override
  String get splashQuote6 => 'Ich bin nicht geizig—ich bin kilowatt-bewusst.';

  @override
  String get splashQuote7 => 'Das Lieblingsereignis deiner Brieftasche: niedrigere Rechnungen.';

  @override
  String get splashQuote8 => 'Neuer Tiefstand bei deiner Rechnung? Das verdient eine Zähler-Feier!';

  @override
  String get splashQuote9 => 'Lekky: der einzige Ort, wo negative Zahlen dich glücklich machen.';

  @override
  String get splashQuote10 => 'Sei kein Widerstand—geh mit dem Strom!';

  @override
  String get dismiss => 'Abbrechen';

  @override
  String get dismissGap => 'Lücke verwerfen';

  @override
  String get dismissEntry => 'Eintrag verwerfen';

  @override
  String get whatThisDoes => 'Was dies bewirkt:';

  @override
  String get noValidationIssues => 'Keine Validierungsprobleme gefunden';

  @override
  String get allDataValid => 'Alle Ihre Daten sind gültig und konsistent';

  @override
  String get refresh => 'Aktualisieren';

  @override
  String get entryNotFound => 'Eintrag nicht gefunden';

  @override
  String get negativeValue => 'Negativer Wert';

  @override
  String get futureDate => 'Zukünftiges Datum';

  @override
  String get chronologicalOrder => 'Chronologische Reihenfolge';

  @override
  String get balanceInconsistency => 'Saldo-Inkonsistenz';

  @override
  String get duplicateEntry => 'Doppelter Eintrag';

  @override
  String get missingEntry => 'Fehlender Eintrag';

  @override
  String get otherIssue => 'Anderes Problem';

  @override
  String dismissGapDescription(Object days) {
    return 'Dies wird die Lücke von $days Tagen verwerfen, indem ein Eintrag für eine Aufzeichnungslücke in Ihrem Verlauf erstellt wird.';
  }

  @override
  String get dismissDuplicateDescription => 'Dies wird den doppelten Eintrag verwerfen, indem er als ignoriert markiert wird. Der Eintrag bleibt in Ihrem Verlauf, wird aber von Validierungsprüfungen ausgeschlossen.';

  @override
  String get dismissGenericDescription => 'Dies wird das Validierungsproblem verwerfen.';

  @override
  String get recordsGapDismissed => 'Aufzeichnungslücke erfolgreich verworfen';

  @override
  String get duplicateEntryDismissed => 'Doppelter Eintrag erfolgreich verworfen';

  @override
  String errorDismissingGap(Object error) {
    return 'Fehler beim Verwerfen der Lücke: $error';
  }

  @override
  String errorDismissingDuplicate(Object error) {
    return 'Fehler beim Verwerfen des doppelten Eintrags: $error';
  }

  @override
  String get reminderServiceUnavailable => 'Erinnerungsdienst vorübergehend nicht verfügbar';

  @override
  String get notificationPermissionRequired => 'Benachrichtigungsberechtigung für Erinnerungen erforderlich';

  @override
  String get unexpectedError => 'Ein unerwarteter Fehler ist aufgetreten';

  @override
  String get waitAndTryAgain => 'Warten Sie einen Moment und versuchen Sie es erneut';

  @override
  String get restartApp => 'Starten Sie die App neu';

  @override
  String get checkInternetConnection => 'Überprüfen Sie die Internetverbindung';

  @override
  String get grantNotificationPermission => 'Erteilen Sie die Benachrichtigungsberechtigung in den Einstellungen';

  @override
  String get enableBackgroundRefresh => 'Aktivieren Sie die Hintergrundaktualisierung';

  @override
  String get disableBatteryOptimization => 'Deaktivieren Sie die Batterieoptimierung für diese App';

  @override
  String get tryAgain => 'Erneut versuchen';

  @override
  String get restartIfPersists => 'Starten Sie die App neu, wenn das Problem weiterhin besteht';

  @override
  String get fileOperationFailed => 'Dateioperation fehlgeschlagen. Bitte versuchen Sie es erneut.';

  @override
  String fieldError(Object field) {
    return 'Feld: $field';
  }

  @override
  String get currentMeterReading => 'Aktuelle Zählerablesung';

  @override
  String get enterCurrentMeterReading => 'Geben Sie Ihre aktuelle Zählerablesung ein';

  @override
  String get enterNumberOnMeter => 'Geben Sie die auf Ihrem Stromzähler angezeigte Zahl ein';

  @override
  String get howToReadMeter => 'So lesen Sie Ihren Zähler ab:';

  @override
  String get locatePrepaIdMeter => 'Finden Sie Ihren Prepaid-Stromzähler.';

  @override
  String get pressDisplayButton => 'Drücken Sie die Anzeige-Taste, bis Sie die aktuelle Ablesung sehen.';

  @override
  String get enterNumberInCurrency => 'Geben Sie die angezeigte Zahl in Ihren Währungseinheiten ein.';

  @override
  String get lookForTotalValue => 'Einige Zähler zeigen mehrere Werte - suchen Sie nach dem mit \"Gesamt\" oder ähnlich beschrifteten.';

  @override
  String get takePhotoTip => 'Tipp: Machen Sie ein Foto Ihres Zählers zur späteren Referenz.';

  @override
  String get meterReadingValidationError => 'Die anfängliche Zählerablesung muss zwischen 0.00 und 999.99 liegen';

  @override
  String get current => 'Aktuell';

  @override
  String get selectLanguageDescription => 'Wählen Sie Ihre bevorzugte Sprache für die App-Oberfläche.';
}
