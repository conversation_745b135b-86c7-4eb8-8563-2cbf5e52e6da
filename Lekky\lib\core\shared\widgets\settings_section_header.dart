import 'package:flutter/material.dart';
import 'section_header.dart';

/// A widget for displaying a section header in settings
class SettingsSectionHeader extends StatelessWidget {
  /// Title of the section
  final String title;

  /// Description of the section
  final String description;

  /// Icon for the section
  final IconData icon;

  /// Constructor
  const SettingsSectionHeader({
    super.key,
    required this.title,
    required this.description,
    required this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return SectionHeader(
      title: title,
      description: description,
      icon: icon,
      style: SectionHeaderStyle.settings,
    );
  }
}
