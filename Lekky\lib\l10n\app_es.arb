{"@@locale": "es", "@@last_modified": "2025-01-17T00:00:00.000Z", "appName": "Le<PERSON><PERSON>", "tagline": "Tu Asistente de Medidor Prepago", "splashQuote": "No soy tacaño, soy consciente de los kilovatios.", "checkingPermissions": "Comprobando permisos...", "initializing": "Inicializando...", "welcomeTitle": "Bienvenido a Lekky", "welcomeSubtitle": "Tu asistente personal de medidor prepago", "trackUsage": "Seguimiento de uso", "getAlerts": "Recibir alertas", "viewHistory": "Ver historial", "calculateCosts": "Calcular costos", "trackUsageDesc": "Monitorea tu consumo y gasto de electricidad", "getAlertsDesc": "Recibe notificaciones cuando tu saldo esté bajo", "viewHistoryDesc": "Mira tus lecturas de medidor y recargas anteriores", "calculateCostsDesc": "Estima tus costos de electricidad en diferentes períodos", "getStarted": "Comenzar", "restoreData": "Restaurar datos anteriores", "restoreHelper": "¿Tienes una copia de seguridad de otro dispositivo?", "restoreDataTitle": "<PERSON><PERSON><PERSON> <PERSON>", "restoreDataContent": "Esta función te permitirá restaurar datos desde un archivo de respaldo.", "cancel": "<PERSON><PERSON><PERSON>", "chooseFile": "Elegir archivo", "regionSettings": "Configuración regional", "language": "Idioma", "currency": "Moneda", "selectLanguage": "Selecciona tu idioma preferido para la interfaz de la aplicación.", "selectCurrency": "Selecciona la moneda para tus lecturas de medidor.", "currencyTip": "Consejo: Selecciona la moneda que coincida con tus facturas de electricidad.", "perDay": "/día", "dashboard": "Panel de Control", "history": "Historial", "settings": "Configuración", "noEntriesFound": "No se encontraron entradas", "tryAdjustingFilters": "Intenta ajustar tus filtros para ver más entradas", "noEntriesYet": "Aún no hay entradas", "addFirstEntry": "Agrega tu primera lectura de medidor o recarga para comenzar", "errorLoadingData": "Error al cargar datos", "errorLoadingPreferences": "Error al cargar preferencias: {error}", "meterReading": "Lectura del Medidor", "topUp": "Recarga", "lastUpdated": "Última Actualización", "daysRemaining": "Días Restantes", "currentBalance": "Saldo Actual", "usageStatistics": "Estadísticas de Uso", "recentAverage": "Promedio Reciente", "totalAverage": "Promedio Total", "dailyUsage": "Uso Diario", "topUpStatistics": "Estadísticas de Recarga", "daysToAlert": "Días para Alerta", "daysToZero": "<PERSON><PERSON> has<PERSON>", "quickActions": "Acciones Rápidas", "addEntry": "Agregar Entrada", "recentActivity": "Actividad Reciente", "viewAll": "<PERSON><PERSON>", "save": "Guardar", "delete": "Eliminar", "edit": "<PERSON><PERSON>", "add": "Agregar", "close": "<PERSON><PERSON><PERSON>", "ok": "OK", "yes": "Sí", "no": "No", "loading": "Cargando...", "saving": "Guardando...", "region": "Región", "languageCurrency": "Idioma, Moneda", "recentAvgUsage": "El promedio reciente muestra el uso entre lecturas consecutivas", "tapNotificationBell": "Toca el icono de la campana de notificaciones para ver todas las notificaciones", "addReadingsRegularly": "Añade nuevas lecturas del medidor regularmente para mejores estadísticas de uso", "setupAlertsLowBalance": "Configura alertas para recibir notificaciones cuando el saldo sea bajo", "useQuickActions": "Usa las Acciones Rápidas para añadir nuevas lecturas o recargas", "viewHistoryTip": "Ve tu historial para ver todas las lecturas del medidor y recargas pasadas", "notificationsGrouped": "Las notificaciones están agrupadas por tipo para una fácil organización", "swipeNotifications": "Desliza a la izquierda en las notificaciones para marcar como leídas, a la derecha para eliminar", "configureThresholds": "Configura los umbrales de notificación en Configuración > Alertas y Notificaciones", "lowBalanceHelp": "Las alertas de saldo bajo te ayudan a evitar quedarte sin crédito", "daysInAdvanceTip": "Establece \\\"Días de Antelación\\\" para recibir recordatorios de recarga temprano", "today": "Hoy", "yesterday": "Ayer", "lastWeek": "La semana pasada", "lastMonth": "El mes pasado", "never": "Nunca", "days": "días", "day": "día", "hours": "horas", "hour": "hora", "minutes": "minutos", "minute": "minuto", "retry": "Reintentar", "skip": "<PERSON><PERSON><PERSON>", "complete": "Completo", "failed": "<PERSON><PERSON>", "syncing": "Sincronizando...", "deleting": "Eliminando...", "noMeterReading": "No hay lectura del medidor disponible", "addFirstReading": "Añade tu primera lectura", "nextTopUp": "Próxima recarga", "addReading": "<PERSON><PERSON><PERSON> lectura", "addTopUp": "<PERSON><PERSON><PERSON> recarga", "noRecentActivity": "No hay actividad reciente", "invalidEntry": "Entrada inválida", "missingData": "<PERSON><PERSON> faltantes", "dataInconsistency": "Inconsistencia de datos", "validationError": "Error de validación", "failedToSave": "<PERSON><PERSON>r al guardar", "networkError": "Error de red", "permissionDenied": "<PERSON><PERSON><PERSON> den<PERSON>ado", "fileNotFound": "Archivo no encontrado", "invalidFileFormat": "Formato de archivo inválido", "addEntryDialog": "<PERSON><PERSON><PERSON> entrada", "editEntry": "Editar entrada", "deleteEntry": "Eliminar entrada", "confirmDelete": "Confirmar eliminación", "exportData": "Exportar datos", "importData": "Importar datos", "settingsDialog": "Configuración", "about": "Acerca de", "lowBalanceAlert": "Alerta de saldo bajo", "timeToTopUp": "<PERSON><PERSON> de recargar", "meterReadingReminder": "Recordatorio de lectura del medidor", "dataBackupReminder": "Recordatorio de respaldo de <PERSON>tos", "alertsNotifications": "Alertas y notificaciones", "dateTime": "<PERSON><PERSON> y hora", "theme": "<PERSON><PERSON>", "dataManagement": "Gestión de datos", "appInformation": "Información de la aplicación", "setup": "Configuración", "setupRegionSettings": "Configuración Regional", "setupRegionSettingsDesc": "Configura las preferencias de idioma y moneda.", "setupInitialMeterReading": "Lectura Inicial del Medidor", "setupInitialMeterReadingDesc": "Ingresa tu lectura actual del medidor para comenzar el seguimiento.", "setupAlertSettings": "Configuración de Alertas", "setupAlertSettingsDesc": "Configura cuándo quieres recibir alertas sobre el saldo de tu medidor.", "setupDateSettings": "Configuración de Fecha", "setupDateSettingsDesc": "Configura cómo se muestran las fechas en la aplicación.", "setupAppearance": "Apariencia", "setupAppearanceDesc": "Personaliza la apariencia de la aplicación.", "finishSetup": "Finalizar Configuración", "setupFailed": "Error en la configuración: {error}", "pleaseCheckInputs": "Por favor verifica tus datos.", "dateSettingsTitle": "Configuración de Fecha", "dateSettingsDesc": "Elige cómo se mostrarán las fechas en toda la aplicación.", "dateFormat": "Formato de Fecha", "alertThreshold": "Umbral de Alerta", "alertThresholdDesc": "Serás notificado cuando tu saldo esté por debajo de esta cantidad.", "daysInAdvance": "Días de Anticipación", "daysInAdvanceDesc": "Cuántos días antes de quedarte sin crédito enviar recordatorios.", "initialMeterReadingOptional": "Esto es opcional. Puedes omitir este paso y agregar tu primera lectura del medidor más tarde.", "errorLoadingSettings": "Error al cargar configuración: {error}", "cost": "Costo", "costOfElectric": "Costo de Electricidad", "notEnoughData": "No hay suficientes datos", "recentAvg": "Promedio Reciente", "totalAvg": "Promedio Total", "splashQuote1": "¿Qué tal? ¡Solo ahorrándote vatios (y euros)!", "splashQuote2": "Mantén la calma y sigue la corriente.", "splashQuote3": "¡Ohm Dios mío, eres eficiente!", "splashQuote4": "Leer medidores: la única vez que ver números bajar es algo bueno.", "splashQuote5": "¿Ahorrar dinero en electricidad? ¡Eso sí que es dinero bien vatiado!", "splashQuote6": "No soy tacaño—soy kilovatio-consciente.", "splashQuote7": "El evento favorito de tu billetera: facturas más bajas.", "splashQuote8": "¿Nuevo mínimo en tu factura? ¡Eso merece una celebración medidora!", "splashQuote9": "Lekky: el único lugar donde los números negativos te hacen feliz.", "splashQuote10": "¡No seas un resistor—ve con la corriente!", "dismiss": "Descar<PERSON>", "@dismiss": {"description": "Etiqueta de botón para descartar un diálogo o acción"}, "@editEntry": {"description": "Etiqueta de botón o diálogo para editar una entrada"}, "dismissGap": "Descartar brecha", "@dismissGap": {"description": "Etiqueta de botón para descartar una brecha de datos"}, "dismissEntry": "Descartar entrada", "@dismissEntry": {"description": "Etiqueta de botón para descartar una entrada"}, "whatThisDoes": "Qué hace esto:", "@whatThisDoes": {"description": "Etiqueta que explica la acción de un botón o característica"}, "noValidationIssues": "No se encontraron problemas de validación", "@noValidationIssues": {"description": "Mensaje que indica que no se encontraron errores de validación"}, "allDataValid": "Todos tus datos son v<PERSON><PERSON><PERSON> y <PERSON>es", "@allDataValid": {"description": "Mensaje que confirma que todos los datos son válidos y consistentes"}, "refresh": "Actualizar", "@refresh": {"description": "Etiqueta de botón para actualizar datos"}, "entryNotFound": "Entrada no encontrada", "@entryNotFound": {"description": "Mensaje que indica que no se encontró una entrada"}, "negativeValue": "Valor Negativo", "futureDate": "<PERSON><PERSON>", "chronologicalOrder": "Orden Cronológico", "balanceInconsistency": "Inconsistencia de Saldo", "duplicateEntry": "Entrada Duplicada", "missingEntry": "Entrada Faltante", "otherIssue": "Otro Problema", "dismissGapDescription": "Esto descartará la brecha de {days} días creando una entrada de Brecha de Registros en tu historial.", "@dismissGapDescription": {"description": "Descripción de descartar una brecha de datos", "placeholders": {"days": {}}}, "dismissDuplicateDescription": "Esto descartará la entrada duplicada marcándola como ignorada. La entrada permanecerá en tu historial pero se excluirá de las verificaciones de validación.", "@dismissDuplicateDescription": {"description": "Descripción de descartar una entrada duplicada"}, "dismissGenericDescription": "Esto descartará el problema de validación.", "@dismissGenericDescription": {"description": "Descripción genérica para descartar un problema de validación"}, "recordsGapDismissed": "Brecha de registros descartada con éxito", "@recordsGapDismissed": {"description": "Mensaje de éxito para descartar una brecha de registros"}, "duplicateEntryDismissed": "Entrada duplicada descartada con éxito", "@duplicateEntryDismissed": {"description": "Mensaje de éxito para descartar una entrada duplicada"}, "errorDismissingGap": "Error al descartar la brecha: {error}", "@errorDismissingGap": {"description": "Mensaje de error al fallar al descartar una brecha", "placeholders": {"error": {}}}, "errorDismissingDuplicate": "Error al descartar la entrada duplicada: {error}", "@errorDismissingDuplicate": {"description": "Mensaje de error al fallar al descartar una entrada duplicada", "placeholders": {"error": {}}}, "reminderServiceUnavailable": "Servicio de recordatorios temporalmente no disponible", "@reminderServiceUnavailable": {"description": "Mensaje de error para servicio de recordatorios no disponible"}, "notificationPermissionRequired": "Se requiere permiso de notificación para recordatorios", "@notificationPermissionRequired": {"description": "Mensaje indicando que se necesita permiso de notificación"}, "unexpectedError": "Ocurrió un error inesperado", "@unexpectedError": {"description": "Mensaje para un error inesperado"}, "waitAndTryAgain": "Espera un momento y vuelve a intentarlo", "@waitAndTryAgain": {"description": "Instrucción para esperar y reintentar"}, "restartApp": "Reinicia la aplicación", "@restartApp": {"description": "Instrucción para reiniciar la aplicación"}, "checkInternetConnection": "Verifica la conexión a internet", "@checkInternetConnection": {"description": "Instrucción para verificar la conexión a internet"}, "grantNotificationPermission": "Concede permiso de notificación en los ajustes", "@grantNotificationPermission": {"description": "Instrucción para conceder permiso de notificación"}, "enableBackgroundRefresh": "Habilita la actualización en segundo plano", "@enableBackgroundRefresh": {"description": "Instrucción para habilitar actualización en segundo plano"}, "disableBatteryOptimization": "Deshabilita la optimización de batería para esta aplicación", "@disableBatteryOptimization": {"description": "Instrucción para deshabilitar optimización de batería"}, "tryAgain": "Intentar de nuevo", "@tryAgain": {"description": "Etiqueta de botón para intentar una acción de nuevo"}, "restartIfPersists": "Reinicia la aplicación si el problema persiste", "@restartIfPersists": {"description": "Instrucción para reiniciar si el problema persiste"}, "fileOperationFailed": "La operación de archivo falló. Por favor, intenta de nuevo.", "@fileOperationFailed": {"description": "Mensaje de error para una operación de archivo fallida"}, "fieldError": "Campo: {field}", "@fieldError": {"description": "Mensaje de error especificando un campo con problema", "placeholders": {"field": {}}}, "currentMeterReading": "Lectura Actual del Medidor", "enterCurrentMeterReading": "Ingresa tu lectura actual del medidor", "enterNumberOnMeter": "Ingresa el número mostrado en tu medidor de electricidad", "howToReadMeter": "<PERSON><PERSON><PERSON> leer tu medidor:", "locatePrepaIdMeter": "Localiza tu medidor de electricidad prepago.", "pressDisplayButton": "Presiona el botón de pantalla hasta que veas la lectura actual.", "enterNumberInCurrency": "Ingresa el número mostrado en tus unidades de moneda.", "lookForTotalValue": "Algunos medidores muestran múltiples valores - busca el etiquetado como \"Total\" o similar.", "takePhotoTip": "Consejo: Toma una foto de tu medidor para referencia futura.", "meterReadingValidationError": "La lectura inicial del medidor debe estar entre 0.00 y 999.99", "current": "Actual", "selectLanguageDescription": "Selecciona tu idioma preferido para la interfaz de la aplicación."}