import 'package:flutter/material.dart';
import 'numeric_input_field.dart';

/// A reusable widget for currency input fields that automatically selects all text when focused
class CurrencyInputField extends StatelessWidget {
  /// The current value of the field
  final double? value;

  /// Callback when the value changes (called on every keystroke)
  final ValueChanged<double?> onChanged;

  /// Callback when editing is complete (called when user taps done or field loses focus)
  final ValueChanged<double?>? onEditingComplete;

  /// Currency symbol to display as prefix
  final String currencySymbol;

  /// Label text for the field
  final String labelText;

  /// Hint text for the field
  final String? hintText;

  /// Helper text for the field
  final String? helperText;

  /// Error text for the field
  final String? errorText;

  /// Whether to allow null values
  final bool allowNull;

  /// Minimum allowed value
  final double minValue;

  /// Maximum allowed value
  final double maxValue;

  /// Number of decimal places to display
  final int decimalPlaces;

  /// Border radius for the input field
  final BorderRadius? borderRadius;

  /// Constructor
  const CurrencyInputField({
    super.key,
    required this.value,
    required this.onChanged,
    required this.currencySymbol,
    required this.labelText,
    this.hintText,
    this.helperText,
    this.errorText,
    this.allowNull = false,
    this.minValue = 0.0,
    this.maxValue = 999.99,
    this.decimalPlaces = 2,
    this.borderRadius,
    this.onEditingComplete,
  });

  @override
  Widget build(BuildContext context) {
    return NumericInputField(
      value: value,
      onChanged: (num? newValue) => onChanged(newValue?.toDouble()),
      onEditingComplete: onEditingComplete != null
          ? (num? newValue) => onEditingComplete!(newValue?.toDouble())
          : null,
      inputType: NumericInputType.currency,
      currencySymbol: currencySymbol,
      labelText: labelText,
      hintText: hintText,
      helperText: helperText,
      errorText: errorText,
      allowNull: allowNull,
      minValue: minValue,
      maxValue: maxValue,
      decimalPlaces: decimalPlaces,
      borderRadius: borderRadius,
    );
  }
}
