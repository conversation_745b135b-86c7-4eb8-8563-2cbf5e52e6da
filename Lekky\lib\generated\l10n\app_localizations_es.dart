import 'app_localizations.dart';

/// The translations for Spanish Castilian (`es`).
class AppLocalizationsEs extends AppLocalizations {
  AppLocalizationsEs([String locale = 'es']) : super(locale);

  @override
  String get appName => 'Lekky';

  @override
  String get tagline => 'Tu Asistente de Medidor Prepago';

  @override
  String get splashQuote => 'No soy tacaño, soy consciente de los kilovatios.';

  @override
  String get checkingPermissions => 'Comprobando permisos...';

  @override
  String get initializing => 'Inicializando...';

  @override
  String get welcomeTitle => 'Bienvenido a Lekky';

  @override
  String get welcomeSubtitle => 'Tu asistente personal de medidor prepago';

  @override
  String get trackUsage => 'Seguimiento de uso';

  @override
  String get getAlerts => 'Recibir alertas';

  @override
  String get viewHistory => 'Ver historial';

  @override
  String get calculateCosts => 'Calcular costos';

  @override
  String get trackUsageDesc => 'Monitorea tu consumo y gasto de electricidad';

  @override
  String get getAlertsDesc => 'Recibe notificaciones cuando tu saldo esté bajo';

  @override
  String get viewHistoryDesc => 'Mira tus lecturas de medidor y recargas anteriores';

  @override
  String get calculateCostsDesc => 'Estima tus costos de electricidad en diferentes períodos';

  @override
  String get getStarted => 'Comenzar';

  @override
  String get restoreData => 'Restaurar datos anteriores';

  @override
  String get restoreHelper => '¿Tienes una copia de seguridad de otro dispositivo?';

  @override
  String get restoreDataTitle => 'Restaurar datos';

  @override
  String get restoreDataContent => 'Esta función te permitirá restaurar datos desde un archivo de respaldo.';

  @override
  String get cancel => 'Cancelar';

  @override
  String get chooseFile => 'Elegir archivo';

  @override
  String get regionSettings => 'Configuración regional';

  @override
  String get language => 'Idioma';

  @override
  String get currency => 'Moneda';

  @override
  String get selectLanguage => 'Selecciona tu idioma preferido para la interfaz de la aplicación.';

  @override
  String get selectCurrency => 'Selecciona la moneda para tus lecturas de medidor.';

  @override
  String get currencyTip => 'Consejo: Selecciona la moneda que coincida con tus facturas de electricidad.';

  @override
  String get perDay => '/día';

  @override
  String get dashboard => 'Panel de Control';

  @override
  String get history => 'Historial';

  @override
  String get settings => 'Configuración';

  @override
  String get noEntriesFound => 'No se encontraron entradas';

  @override
  String get tryAdjustingFilters => 'Intenta ajustar tus filtros para ver más entradas';

  @override
  String get noEntriesYet => 'Aún no hay entradas';

  @override
  String get addFirstEntry => 'Agrega tu primera lectura de medidor o recarga para comenzar';

  @override
  String get errorLoadingData => 'Error al cargar datos';

  @override
  String errorLoadingPreferences(String error) {
    return 'Error al cargar preferencias: $error';
  }

  @override
  String get meterReading => 'Lectura del Medidor';

  @override
  String get topUp => 'Recarga';

  @override
  String get lastUpdated => 'Última Actualización';

  @override
  String get daysRemaining => 'Días Restantes';

  @override
  String get currentBalance => 'Saldo Actual';

  @override
  String get usageStatistics => 'Estadísticas de Uso';

  @override
  String get recentAverage => 'Promedio Reciente';

  @override
  String get totalAverage => 'Promedio Total';

  @override
  String get dailyUsage => 'Uso Diario';

  @override
  String get topUpStatistics => 'Estadísticas de Recarga';

  @override
  String get daysToAlert => 'Días para Alerta';

  @override
  String get daysToZero => 'Días hasta Cero';

  @override
  String get quickActions => 'Acciones Rápidas';

  @override
  String get addEntry => 'Agregar Entrada';

  @override
  String get recentActivity => 'Actividad Reciente';

  @override
  String get viewAll => 'Ver Todo';

  @override
  String get save => 'Guardar';

  @override
  String get delete => 'Eliminar';

  @override
  String get edit => 'Editar';

  @override
  String get add => 'Agregar';

  @override
  String get close => 'Cerrar';

  @override
  String get ok => 'OK';

  @override
  String get yes => 'Sí';

  @override
  String get no => 'No';

  @override
  String get loading => 'Cargando...';

  @override
  String get saving => 'Guardando...';

  @override
  String get region => 'Región';

  @override
  String get languageCurrency => 'Idioma, Moneda';

  @override
  String get recentAvgUsage => 'El promedio reciente muestra el uso entre lecturas consecutivas';

  @override
  String get tapNotificationBell => 'Toca el icono de la campana de notificaciones para ver todas las notificaciones';

  @override
  String get addReadingsRegularly => 'Añade nuevas lecturas del medidor regularmente para mejores estadísticas de uso';

  @override
  String get setupAlertsLowBalance => 'Configura alertas para recibir notificaciones cuando el saldo sea bajo';

  @override
  String get useQuickActions => 'Usa las Acciones Rápidas para añadir nuevas lecturas o recargas';

  @override
  String get viewHistoryTip => 'Ve tu historial para ver todas las lecturas del medidor y recargas pasadas';

  @override
  String get notificationsGrouped => 'Las notificaciones están agrupadas por tipo para una fácil organización';

  @override
  String get swipeNotifications => 'Desliza a la izquierda en las notificaciones para marcar como leídas, a la derecha para eliminar';

  @override
  String get configureThresholds => 'Configura los umbrales de notificación en Configuración > Alertas y Notificaciones';

  @override
  String get lowBalanceHelp => 'Las alertas de saldo bajo te ayudan a evitar quedarte sin crédito';

  @override
  String get daysInAdvanceTip => 'Establece \\\"Días de Antelación\\\" para recibir recordatorios de recarga temprano';

  @override
  String get today => 'Hoy';

  @override
  String get yesterday => 'Ayer';

  @override
  String get lastWeek => 'La semana pasada';

  @override
  String get lastMonth => 'El mes pasado';

  @override
  String get never => 'Nunca';

  @override
  String get days => 'días';

  @override
  String get day => 'día';

  @override
  String get hours => 'horas';

  @override
  String get hour => 'hora';

  @override
  String get minutes => 'minutos';

  @override
  String get minute => 'minuto';

  @override
  String get retry => 'Reintentar';

  @override
  String get skip => 'Omitir';

  @override
  String get complete => 'Completo';

  @override
  String get failed => 'Falló';

  @override
  String get syncing => 'Sincronizando...';

  @override
  String get deleting => 'Eliminando...';

  @override
  String get noMeterReading => 'No hay lectura del medidor disponible';

  @override
  String get addFirstReading => 'Añade tu primera lectura';

  @override
  String get nextTopUp => 'Próxima recarga';

  @override
  String get addReading => 'Añadir lectura';

  @override
  String get addTopUp => 'Añadir recarga';

  @override
  String get noRecentActivity => 'No hay actividad reciente';

  @override
  String get invalidEntry => 'Entrada inválida';

  @override
  String get missingData => 'Datos faltantes';

  @override
  String get dataInconsistency => 'Inconsistencia de datos';

  @override
  String get validationError => 'Error de validación';

  @override
  String get failedToSave => 'Error al guardar';

  @override
  String get networkError => 'Error de red';

  @override
  String get permissionDenied => 'Permiso denegado';

  @override
  String get fileNotFound => 'Archivo no encontrado';

  @override
  String get invalidFileFormat => 'Formato de archivo inválido';

  @override
  String get addEntryDialog => 'Añadir entrada';

  @override
  String get editEntry => 'Editar entrada';

  @override
  String get deleteEntry => 'Eliminar entrada';

  @override
  String get confirmDelete => 'Confirmar eliminación';

  @override
  String get exportData => 'Exportar datos';

  @override
  String get importData => 'Importar datos';

  @override
  String get settingsDialog => 'Configuración';

  @override
  String get about => 'Acerca de';

  @override
  String get lowBalanceAlert => 'Alerta de saldo bajo';

  @override
  String get timeToTopUp => 'Hora de recargar';

  @override
  String get meterReadingReminder => 'Recordatorio de lectura del medidor';

  @override
  String get dataBackupReminder => 'Recordatorio de respaldo de datos';

  @override
  String get alertsNotifications => 'Alertas y notificaciones';

  @override
  String get dateTime => 'Fecha y hora';

  @override
  String get theme => 'Tema';

  @override
  String get dataManagement => 'Gestión de datos';

  @override
  String get appInformation => 'Información de la aplicación';

  @override
  String get setup => 'Configuración';

  @override
  String get setupRegionSettings => 'Configuración Regional';

  @override
  String get setupRegionSettingsDesc => 'Configura las preferencias de idioma y moneda.';

  @override
  String get setupInitialMeterReading => 'Lectura Inicial del Medidor';

  @override
  String get setupInitialMeterReadingDesc => 'Ingresa tu lectura actual del medidor para comenzar el seguimiento.';

  @override
  String get setupAlertSettings => 'Configuración de Alertas';

  @override
  String get setupAlertSettingsDesc => 'Configura cuándo quieres recibir alertas sobre el saldo de tu medidor.';

  @override
  String get setupDateSettings => 'Configuración de Fecha';

  @override
  String get setupDateSettingsDesc => 'Configura cómo se muestran las fechas en la aplicación.';

  @override
  String get setupAppearance => 'Apariencia';

  @override
  String get setupAppearanceDesc => 'Personaliza la apariencia de la aplicación.';

  @override
  String get finishSetup => 'Finalizar Configuración';

  @override
  String setupFailed(String error) {
    return 'Error en la configuración: $error';
  }

  @override
  String get pleaseCheckInputs => 'Por favor verifica tus datos.';

  @override
  String get dateSettingsTitle => 'Configuración de Fecha';

  @override
  String get dateSettingsDesc => 'Elige cómo se mostrarán las fechas en toda la aplicación.';

  @override
  String get dateFormat => 'Formato de Fecha';

  @override
  String get alertThreshold => 'Umbral de Alerta';

  @override
  String get alertThresholdDesc => 'Serás notificado cuando tu saldo esté por debajo de esta cantidad.';

  @override
  String get daysInAdvance => 'Días de Anticipación';

  @override
  String get daysInAdvanceDesc => 'Cuántos días antes de quedarte sin crédito enviar recordatorios.';

  @override
  String get initialMeterReadingOptional => 'Esto es opcional. Puedes omitir este paso y agregar tu primera lectura del medidor más tarde.';

  @override
  String errorLoadingSettings(String error) {
    return 'Error al cargar configuración: $error';
  }

  @override
  String get cost => 'Costo';

  @override
  String get costOfElectric => 'Costo de Electricidad';

  @override
  String get notEnoughData => 'No hay suficientes datos';

  @override
  String get recentAvg => 'Promedio Reciente';

  @override
  String get totalAvg => 'Promedio Total';

  @override
  String get splashQuote1 => '¿Qué tal? ¡Solo ahorrándote vatios (y euros)!';

  @override
  String get splashQuote2 => 'Mantén la calma y sigue la corriente.';

  @override
  String get splashQuote3 => '¡Ohm Dios mío, eres eficiente!';

  @override
  String get splashQuote4 => 'Leer medidores: la única vez que ver números bajar es algo bueno.';

  @override
  String get splashQuote5 => '¿Ahorrar dinero en electricidad? ¡Eso sí que es dinero bien vatiado!';

  @override
  String get splashQuote6 => 'No soy tacaño—soy kilovatio-consciente.';

  @override
  String get splashQuote7 => 'El evento favorito de tu billetera: facturas más bajas.';

  @override
  String get splashQuote8 => '¿Nuevo mínimo en tu factura? ¡Eso merece una celebración medidora!';

  @override
  String get splashQuote9 => 'Lekky: el único lugar donde los números negativos te hacen feliz.';

  @override
  String get splashQuote10 => '¡No seas un resistor—ve con la corriente!';

  @override
  String get dismiss => 'Descartar';

  @override
  String get dismissGap => 'Descartar brecha';

  @override
  String get dismissEntry => 'Descartar entrada';

  @override
  String get whatThisDoes => 'Qué hace esto:';

  @override
  String get noValidationIssues => 'No se encontraron problemas de validación';

  @override
  String get allDataValid => 'Todos tus datos son válidos y consistentes';

  @override
  String get refresh => 'Actualizar';

  @override
  String get entryNotFound => 'Entrada no encontrada';

  @override
  String get negativeValue => 'Valor Negativo';

  @override
  String get futureDate => 'Fecha Futura';

  @override
  String get chronologicalOrder => 'Orden Cronológico';

  @override
  String get balanceInconsistency => 'Inconsistencia de Saldo';

  @override
  String get duplicateEntry => 'Entrada Duplicada';

  @override
  String get missingEntry => 'Entrada Faltante';

  @override
  String get otherIssue => 'Otro Problema';

  @override
  String dismissGapDescription(Object days) {
    return 'Esto descartará la brecha de $days días creando una entrada de Brecha de Registros en tu historial.';
  }

  @override
  String get dismissDuplicateDescription => 'Esto descartará la entrada duplicada marcándola como ignorada. La entrada permanecerá en tu historial pero se excluirá de las verificaciones de validación.';

  @override
  String get dismissGenericDescription => 'Esto descartará el problema de validación.';

  @override
  String get recordsGapDismissed => 'Brecha de registros descartada con éxito';

  @override
  String get duplicateEntryDismissed => 'Entrada duplicada descartada con éxito';

  @override
  String errorDismissingGap(Object error) {
    return 'Error al descartar la brecha: $error';
  }

  @override
  String errorDismissingDuplicate(Object error) {
    return 'Error al descartar la entrada duplicada: $error';
  }

  @override
  String get reminderServiceUnavailable => 'Servicio de recordatorios temporalmente no disponible';

  @override
  String get notificationPermissionRequired => 'Se requiere permiso de notificación para recordatorios';

  @override
  String get unexpectedError => 'Ocurrió un error inesperado';

  @override
  String get waitAndTryAgain => 'Espera un momento y vuelve a intentarlo';

  @override
  String get restartApp => 'Reinicia la aplicación';

  @override
  String get checkInternetConnection => 'Verifica la conexión a internet';

  @override
  String get grantNotificationPermission => 'Concede permiso de notificación en los ajustes';

  @override
  String get enableBackgroundRefresh => 'Habilita la actualización en segundo plano';

  @override
  String get disableBatteryOptimization => 'Deshabilita la optimización de batería para esta aplicación';

  @override
  String get tryAgain => 'Intentar de nuevo';

  @override
  String get restartIfPersists => 'Reinicia la aplicación si el problema persiste';

  @override
  String get fileOperationFailed => 'La operación de archivo falló. Por favor, intenta de nuevo.';

  @override
  String fieldError(Object field) {
    return 'Campo: $field';
  }

  @override
  String get currentMeterReading => 'Lectura Actual del Medidor';

  @override
  String get enterCurrentMeterReading => 'Ingresa tu lectura actual del medidor';

  @override
  String get enterNumberOnMeter => 'Ingresa el número mostrado en tu medidor de electricidad';

  @override
  String get howToReadMeter => 'Cómo leer tu medidor:';

  @override
  String get locatePrepaIdMeter => 'Localiza tu medidor de electricidad prepago.';

  @override
  String get pressDisplayButton => 'Presiona el botón de pantalla hasta que veas la lectura actual.';

  @override
  String get enterNumberInCurrency => 'Ingresa el número mostrado en tus unidades de moneda.';

  @override
  String get lookForTotalValue => 'Algunos medidores muestran múltiples valores - busca el etiquetado como \"Total\" o similar.';

  @override
  String get takePhotoTip => 'Consejo: Toma una foto de tu medidor para referencia futura.';

  @override
  String get meterReadingValidationError => 'La lectura inicial del medidor debe estar entre 0.00 y 999.99';

  @override
  String get current => 'Actual';

  @override
  String get selectLanguageDescription => 'Selecciona tu idioma preferido para la interfaz de la aplicación.';
}
