import 'package:flutter/material.dart';
import '../../../../core/shared/widgets/currency_input_field.dart';
import '../../../../generated/l10n/app_localizations.dart';
import 'setup_section_header.dart';
import 'info_notice.dart';

/// A widget for meter reading settings in the setup screen
class MeterReadingCard extends StatelessWidget {
  /// Current initial meter reading
  final double? initialMeterReading;

  /// Callback when initial meter reading changes
  final Function(double?) onInitialMeterReadingChanged;

  /// Currency symbol to use
  final String currencySymbol;

  /// Constructor
  const MeterReadingCard({
    super.key,
    required this.initialMeterReading,
    required this.onInitialMeterReadingChanged,
    this.currencySymbol = '£',
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(8.0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SetupSectionHeader(
              title: AppLocalizations.of(context).setupInitialMeterReading,
              description:
                  AppLocalizations.of(context).setupInitialMeterReadingDesc,
              icon: Icons.speed,
            ),

            InfoNotice(
              message: AppLocalizations.of(context).initialMeterReadingOptional,
              icon: Icons.info_outline,
            ),

            const SizedBox(height: 16),

            CurrencyInputField(
              value: initialMeterReading,
              onChanged: onInitialMeterReadingChanged,
              currencySymbol: currencySymbol,
              labelText: AppLocalizations.of(context).currentMeterReading,
              hintText: AppLocalizations.of(context).enterCurrentMeterReading,
              helperText: AppLocalizations.of(context).enterNumberOnMeter,
              minValue: 0.0,
              maxValue: 9999.99,
              allowNull: true,
              borderRadius: BorderRadius.circular(8),
            ),

            const SizedBox(height: 16),

            Text(
              AppLocalizations.of(context).howToReadMeter,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),

            // Meter reading instructions
            _buildInstructionStep(
              context,
              '1',
              AppLocalizations.of(context).locatePrepaIdMeter,
            ),
            _buildInstructionStep(
              context,
              '2',
              AppLocalizations.of(context).pressDisplayButton,
            ),
            _buildInstructionStep(
              context,
              '3',
              AppLocalizations.of(context).enterNumberInCurrency,
            ),
            _buildInstructionStep(
              context,
              '4',
              AppLocalizations.of(context).lookForTotalValue,
            ),

            const SizedBox(height: 16),

            Text(
              AppLocalizations.of(context).takePhotoTip,
              style: TextStyle(
                fontSize: 12,
                fontStyle: FontStyle.italic,
                color: Theme.of(context).textTheme.bodySmall?.color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInstructionStep(
      BuildContext context, String number, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                number,
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onPrimary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(text),
          ),
        ],
      ),
    );
  }
}
