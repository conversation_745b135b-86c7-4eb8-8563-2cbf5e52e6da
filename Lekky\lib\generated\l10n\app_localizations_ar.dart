import 'app_localizations.dart';

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get appName => 'ليكي';

  @override
  String get tagline => 'مساعدك لعداد الكهرباء المدفوع مسبقاً';

  @override
  String get splashQuote => 'لست بخيلاً—أنا واعٍ بالكيلوواط.';

  @override
  String get checkingPermissions => 'جاري فحص الأذونات...';

  @override
  String get initializing => 'جاري التهيئة...';

  @override
  String get welcomeTitle => 'مرحباً بك في Lekky';

  @override
  String get welcomeSubtitle => 'مساعدك الشخصي للعداد المدفوع مسبقاً';

  @override
  String get trackUsage => 'تتبع استهلاكك';

  @override
  String get getAlerts => 'احصل على تنبيهات في الوقت المناسب';

  @override
  String get viewHistory => 'عرض السجل';

  @override
  String get calculateCosts => 'حساب التكاليف';

  @override
  String get trackUsageDesc => 'راقب استهلاك الكهرباء وإنفاقك';

  @override
  String get getAlertsDesc => 'احصل على إشعارات عندما ينخفض رصيدك';

  @override
  String get viewHistoryDesc => 'اطلع على قراءات العداد وعمليات الشحن السابقة';

  @override
  String get calculateCostsDesc => 'قدر تكاليف الكهرباء على فترات مختلفة';

  @override
  String get getStarted => 'ابدأ';

  @override
  String get restoreData => 'استعادة البيانات السابقة';

  @override
  String get restoreHelper => 'هل لديك نسخة احتياطية من جهاز آخر؟';

  @override
  String get restoreDataTitle => 'استعادة البيانات';

  @override
  String get restoreDataContent => 'ستتيح لك هذه الميزة استعادة البيانات من ملف النسخ الاحتياطي.';

  @override
  String get cancel => 'إلغاء';

  @override
  String get chooseFile => 'اختيار ملف';

  @override
  String get regionSettings => 'الإعدادات الإقليمية';

  @override
  String get language => 'اللغة';

  @override
  String get currency => 'العملة';

  @override
  String get selectLanguage => 'اختر لغتك المفضلة لواجهة التطبيق.';

  @override
  String get selectCurrency => 'اختر العملة لقراءات العداد.';

  @override
  String get currencyTip => 'نصيحة: اختر العملة التي تتطابق مع فواتير الكهرباء.';

  @override
  String get perDay => '/يوم';

  @override
  String get dashboard => 'لوحة التحكم';

  @override
  String get history => 'السجل';

  @override
  String get settings => 'الإعدادات';

  @override
  String get noEntriesFound => 'لم يتم العثور على إدخالات';

  @override
  String get tryAdjustingFilters => 'حاول تعديل المرشحات لرؤية المزيد من الإدخالات';

  @override
  String get noEntriesYet => 'لا توجد إدخالات بعد';

  @override
  String get addFirstEntry => 'أضف أول قراءة عداد أو شحن للبدء';

  @override
  String get errorLoadingData => 'خطأ في تحميل البيانات';

  @override
  String errorLoadingPreferences(String error) {
    return 'خطأ في تحميل التفضيلات: $error';
  }

  @override
  String get meterReading => 'قراءة العداد';

  @override
  String get topUp => 'شحن';

  @override
  String get lastUpdated => 'آخر تحديث';

  @override
  String get daysRemaining => 'الأيام المتبقية';

  @override
  String get currentBalance => 'الرصيد الحالي';

  @override
  String get usageStatistics => 'إحصائيات الاستهلاك';

  @override
  String get recentAverage => 'المتوسط الحديث';

  @override
  String get totalAverage => 'المتوسط الإجمالي';

  @override
  String get dailyUsage => 'الاستهلاك اليومي';

  @override
  String get topUpStatistics => 'إحصائيات الشحن';

  @override
  String get daysToAlert => 'أيام حتى التنبيه';

  @override
  String get daysToZero => 'أيام حتى الصفر';

  @override
  String get quickActions => 'إجراءات سريعة';

  @override
  String get addEntry => 'إضافة إدخال';

  @override
  String get recentActivity => 'النشاط الحديث';

  @override
  String get viewAll => 'عرض الكل';

  @override
  String get save => 'حفظ';

  @override
  String get delete => 'حذف';

  @override
  String get edit => 'تحرير';

  @override
  String get add => 'إضافة';

  @override
  String get close => 'إغلاق';

  @override
  String get ok => 'موافق';

  @override
  String get yes => 'نعم';

  @override
  String get no => 'لا';

  @override
  String get loading => 'جاري التحميل...';

  @override
  String get saving => 'جاري الحفظ...';

  @override
  String get region => 'المنطقة';

  @override
  String get languageCurrency => 'اللغة، العملة';

  @override
  String get recentAvgUsage => 'يُظهر المتوسط الحديث الاستخدام بين القراءات المتتالية';

  @override
  String get tapNotificationBell => 'اضغط على أيقونة جرس الإشعارات لعرض جميع الإشعارات';

  @override
  String get addReadingsRegularly => 'أضف قراءات عداد جديدة بانتظام للحصول على إحصائيات استخدام أفضل';

  @override
  String get setupAlertsLowBalance => 'قم بإعداد التنبيهات ليتم إعلامك عندما يكون رصيدك منخفضًا';

  @override
  String get useQuickActions => 'استخدم الإجراءات السريعة لإضافة قراءات أو شحنات جديدة';

  @override
  String get viewHistoryTip => 'اعرض تاريخك لرؤية جميع قراءات العداد والشحنات السابقة';

  @override
  String get notificationsGrouped => 'الإشعارات مجمعة حسب النوع لسهولة التنظيم';

  @override
  String get swipeNotifications => 'اسحب يمينًا على الإشعارات لتمييزها كمقروءة، يسارًا للحذف';

  @override
  String get configureThresholds => 'قم بتكوين عتبات الإشعارات في الإعدادات > التنبيهات والإشعارات';

  @override
  String get lowBalanceHelp => 'تنبيهات الرصيد المنخفض تساعدك على تجنب نفاد الرصيد';

  @override
  String get daysInAdvanceTip => 'اضبط \\\"أيام مسبقًا\\\" للحصول على تذكيرات الشحن مبكرًا';

  @override
  String get today => 'اليوم';

  @override
  String get yesterday => 'أمس';

  @override
  String get lastWeek => 'الأسبوع الماضي';

  @override
  String get lastMonth => 'الشهر الماضي';

  @override
  String get never => 'أبدًا';

  @override
  String get days => 'أيام';

  @override
  String get day => 'يوم';

  @override
  String get hours => 'ساعات';

  @override
  String get hour => 'ساعة';

  @override
  String get minutes => 'دقائق';

  @override
  String get minute => 'دقيقة';

  @override
  String get retry => 'إعادة المحاولة';

  @override
  String get skip => 'تخطي';

  @override
  String get complete => 'مكتمل';

  @override
  String get failed => 'فشل';

  @override
  String get syncing => 'جاري المزامنة...';

  @override
  String get deleting => 'جاري الحذف...';

  @override
  String get noMeterReading => 'لا توجد قراءة عداد متاحة';

  @override
  String get addFirstReading => 'أضف قراءتك الأولى';

  @override
  String get nextTopUp => 'التعبئة التالية';

  @override
  String get addReading => 'إضافة قراءة';

  @override
  String get addTopUp => 'إضافة تعبئة';

  @override
  String get noRecentActivity => 'لا يوجد نشاط أخير';

  @override
  String get invalidEntry => 'إدخال غير صالح';

  @override
  String get missingData => 'بيانات مفقودة';

  @override
  String get dataInconsistency => 'عدم تناسق البيانات';

  @override
  String get validationError => 'خطأ التحقق';

  @override
  String get failedToSave => 'فشل في الحفظ';

  @override
  String get networkError => 'خطأ في الشبكة';

  @override
  String get permissionDenied => 'تم رفض الإذن';

  @override
  String get fileNotFound => 'الملف غير موجود';

  @override
  String get invalidFileFormat => 'تنسيق الملف غير صالح';

  @override
  String get addEntryDialog => 'إضافة إدخال';

  @override
  String get editEntry => 'تعديل إدخال';

  @override
  String get deleteEntry => 'حذف إدخال';

  @override
  String get confirmDelete => 'تأكيد الحذف';

  @override
  String get exportData => 'تصدير البيانات';

  @override
  String get importData => 'استيراد البيانات';

  @override
  String get settingsDialog => 'الإعدادات';

  @override
  String get about => 'حول';

  @override
  String get lowBalanceAlert => 'تنبيه الرصيد المنخفض';

  @override
  String get timeToTopUp => 'حان وقت التعبئة';

  @override
  String get meterReadingReminder => 'تذكير قراءة العداد';

  @override
  String get dataBackupReminder => 'تذكير نسخ البيانات احتياطيًا';

  @override
  String get alertsNotifications => 'التنبيهات والإشعارات';

  @override
  String get dateTime => 'التاريخ والوقت';

  @override
  String get theme => 'الثيم';

  @override
  String get dataManagement => 'إدارة البيانات';

  @override
  String get appInformation => 'معلومات التطبيق';

  @override
  String get setup => 'الإعداد';

  @override
  String get setupRegionSettings => 'إعدادات المنطقة';

  @override
  String get setupRegionSettingsDesc => 'تكوين تفضيلات اللغة والعملة.';

  @override
  String get setupInitialMeterReading => 'قراءة العداد الأولية';

  @override
  String get setupInitialMeterReadingDesc => 'أدخل قراءة العداد الحالية لبدء التتبع.';

  @override
  String get setupAlertSettings => 'إعدادات التنبيه';

  @override
  String get setupAlertSettingsDesc => 'تكوين متى تريد تلقي تنبيهات حول رصيد العداد.';

  @override
  String get setupDateSettings => 'إعدادات التاريخ';

  @override
  String get setupDateSettingsDesc => 'تكوين كيفية عرض التواريخ في التطبيق.';

  @override
  String get setupAppearance => 'المظهر';

  @override
  String get setupAppearanceDesc => 'تخصيص مظهر التطبيق.';

  @override
  String get finishSetup => 'إنهاء الإعداد';

  @override
  String setupFailed(String error) {
    return 'فشل الإعداد: $error';
  }

  @override
  String get pleaseCheckInputs => 'يرجى التحقق من مدخلاتك.';

  @override
  String get dateSettingsTitle => 'إعدادات التاريخ';

  @override
  String get dateSettingsDesc => 'اختر كيفية عرض التواريخ في جميع أنحاء التطبيق.';

  @override
  String get dateFormat => 'تنسيق التاريخ';

  @override
  String get alertThreshold => 'عتبة التنبيه';

  @override
  String get alertThresholdDesc => 'سيتم إشعارك عندما ينخفض رصيدك عن هذا المبلغ.';

  @override
  String get daysInAdvance => 'أيام مسبقة';

  @override
  String get daysInAdvanceDesc => 'كم يوماً قبل نفاد الرصيد لإرسال التذكيرات.';

  @override
  String get initialMeterReadingOptional => 'هذا اختياري. يمكنك تخطي هذه الخطوة وإضافة قراءة العداد الأولى لاحقاً.';

  @override
  String errorLoadingSettings(String error) {
    return 'خطأ في تحميل الإعدادات: $error';
  }

  @override
  String get cost => 'التكلفة';

  @override
  String get costOfElectric => 'تكلفة الكهرباء';

  @override
  String get notEnoughData => 'بيانات غير كافية';

  @override
  String get recentAvg => 'المتوسط الحديث';

  @override
  String get totalAvg => 'المتوسط الكلي';

  @override
  String get splashQuote1 => 'ما الأخبار؟ فقط نوفر لك الواط (والجنيهات)!';

  @override
  String get splashQuote2 => 'ابق هادئاً واتبع التيار.';

  @override
  String get splashQuote3 => 'يا إلهي، أنت فعال!';

  @override
  String get splashQuote4 => 'قراءة العدادات: المرة الوحيدة التي يكون فيها انخفاض الأرقام أمراً جيداً.';

  @override
  String get splashQuote5 => 'توفير المال على الكهرباء؟ هذا مال موفر بالواط!';

  @override
  String get splashQuote6 => 'لست بخيلاً—أنا واعي بالكيلوواط.';

  @override
  String get splashQuote7 => 'الحدث المفضل لمحفظتك: فواتير أقل.';

  @override
  String get splashQuote8 => 'أدنى مستوى جديد في فاتورتك؟ هذا يستحق احتفال العداد!';

  @override
  String get splashQuote9 => 'ليكي: المكان الوحيد حيث الأرقام السالبة تجعلك سعيداً.';

  @override
  String get splashQuote10 => 'لا تكن مقاوماً—اذهب مع التيار!';

  @override
  String get dismiss => 'رفض';

  @override
  String get dismissGap => 'رفض الفجوة';

  @override
  String get dismissEntry => 'رفض الإدخال';

  @override
  String get whatThisDoes => 'ماذا يفعل هذا:';

  @override
  String get noValidationIssues => 'لم يتم العثور على مشكلات تحقق';

  @override
  String get allDataValid => 'جميع بياناتك صحيحة ومتسقة';

  @override
  String get refresh => 'تحديث';

  @override
  String get entryNotFound => 'الإدخال غير موجود';

  @override
  String get negativeValue => 'قيمة سالبة';

  @override
  String get futureDate => 'تاريخ مستقبلي';

  @override
  String get chronologicalOrder => 'الترتيب الزمني';

  @override
  String get balanceInconsistency => 'عدم تطابق الرصيد';

  @override
  String get duplicateEntry => 'إدخال مكرر';

  @override
  String get missingEntry => 'إدخال مفقود';

  @override
  String get otherIssue => 'مشكلة أخرى';

  @override
  String dismissGapDescription(Object days) {
    return 'سيؤدي هذا إلى رفض الفجوة لمدة $days يومًا عن طريق إنشاء إدخال فجوة السجلات في سجلك.';
  }

  @override
  String get dismissDuplicateDescription => 'سيؤدي هذا إلى رفض الإدخال المكرر عن طريق تعليمه كمتجاهل. سيبقى الإدخال في سجلك ولكنه سيُستبعد من فحوصات التحقق.';

  @override
  String get dismissGenericDescription => 'سيؤدي هذا إلى رفض مشكلة التحقق.';

  @override
  String get recordsGapDismissed => 'تم رفض فجوة السجلات بنجاح';

  @override
  String get duplicateEntryDismissed => 'تم رفض الإدخال المكرر بنجاح';

  @override
  String errorDismissingGap(Object error) {
    return 'خطأ في رفض الفجوة: $error';
  }

  @override
  String errorDismissingDuplicate(Object error) {
    return 'خطأ في رفض الإدخال المكرر: $error';
  }

  @override
  String get reminderServiceUnavailable => 'خدمة التذكير غير متاحة مؤقتًا';

  @override
  String get notificationPermissionRequired => 'مطلوب إذن الإشعارات للتذكيرات';

  @override
  String get unexpectedError => 'حدث خطأ غير متوقع';

  @override
  String get waitAndTryAgain => 'انتظر لحظة وحاول مرة أخرى';

  @override
  String get restartApp => 'أعد تشغيل التطبيق';

  @override
  String get checkInternetConnection => 'تحقق من اتصال الإنترنت';

  @override
  String get grantNotificationPermission => 'امنح إذن الإشعارات في الإعدادات';

  @override
  String get enableBackgroundRefresh => 'فعّل تحديث التطبيق في الخلفية';

  @override
  String get disableBatteryOptimization => 'عطّل تحسين البطارية لهذا التطبيق';

  @override
  String get tryAgain => 'حاول مرة أخرى';

  @override
  String get restartIfPersists => 'أعد تشغيل التطبيق إذا استمرت المشكلة';

  @override
  String get fileOperationFailed => 'فشلت عملية الملف. الرجاء المحاولة مرة أخرى.';

  @override
  String fieldError(Object field) {
    return 'الحقل: $field';
  }

  @override
  String get currentMeterReading => 'قراءة العداد الحالية';

  @override
  String get enterCurrentMeterReading => 'أدخل قراءة العداد الحالية';

  @override
  String get enterNumberOnMeter => 'أدخل الرقم المعروض على عداد الكهرباء';

  @override
  String get howToReadMeter => 'كيفية قراءة العداد:';

  @override
  String get locatePrepaIdMeter => 'حدد موقع عداد الكهرباء المدفوع مسبقاً.';

  @override
  String get pressDisplayButton => 'اضغط على زر العرض حتى ترى القراءة الحالية.';

  @override
  String get enterNumberInCurrency => 'أدخل الرقم المعروض بوحدات العملة الخاصة بك.';

  @override
  String get lookForTotalValue => 'بعض العدادات تظهر قيم متعددة - ابحث عن المسمى \"المجموع\" أو ما شابه.';

  @override
  String get takePhotoTip => 'نصيحة: التقط صورة لعدادك للرجوع إليها مستقبلاً.';

  @override
  String get meterReadingValidationError => 'يجب أن تكون قراءة العداد الأولية بين 0.00 و 999.99';

  @override
  String get current => 'الحالي';

  @override
  String get selectLanguageDescription => 'اختر لغتك المفضلة لواجهة التطبيق.';
}
