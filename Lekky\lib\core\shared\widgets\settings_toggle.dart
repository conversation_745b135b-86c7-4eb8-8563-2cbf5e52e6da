import 'package:flutter/material.dart';

/// A widget for toggling settings with enhanced features
class SettingsToggle extends StatelessWidget {
  /// Title of the setting
  final String title;

  /// Description of the setting
  final String? description;

  /// Current value of the toggle
  final bool value;

  /// Callback when value changes
  final Function(bool) onChanged;

  /// Icon to display (optional)
  final IconData? icon;

  /// Whether the toggle is disabled
  final bool isDisabled;

  /// Whether the toggle is in loading state
  final bool isLoading;

  /// Error message to display
  final String? errorText;

  /// Explanation text when disabled
  final String? disabledExplanation;

  /// Constructor
  const SettingsToggle({
    super.key,
    required this.title,
    this.description,
    required this.value,
    required this.onChanged,
    this.icon,
    this.isDisabled = false,
    this.isLoading = false,
    this.errorText,
    this.disabledExplanation,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              if (icon != null) ...[
                Icon(
                  icon,
                  color: isDisabled
                      ? Theme.of(context).disabledColor
                      : Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 16),
              ],
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          title,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            color: isDisabled
                                ? Theme.of(context).disabledColor
                                : null,
                          ),
                        ),
                        if (isLoading) ...[
                          const SizedBox(width: 8),
                          const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                        ],
                      ],
                    ),
                    if (description != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        description!,
                        style: TextStyle(
                          fontSize: 14,
                          color: isDisabled
                              ? Theme.of(context).disabledColor
                              : Theme.of(context).textTheme.bodySmall?.color,
                        ),
                      ),
                    ],
                    if (isDisabled && disabledExplanation != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        disabledExplanation!,
                        style: TextStyle(
                          fontSize: 12,
                          color: Theme.of(context).colorScheme.secondary,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              Switch(
                value: value,
                onChanged: isDisabled ? null : onChanged,
                activeColor: Theme.of(context).colorScheme.primary,
              ),
            ],
          ),
          if (errorText != null) ...[
            const SizedBox(height: 8),
            Text(
              errorText!,
              style: TextStyle(
                color: Theme.of(context).colorScheme.error,
                fontSize: 12,
              ),
            ),
          ],
        ],
      ),
    );
  }
}
