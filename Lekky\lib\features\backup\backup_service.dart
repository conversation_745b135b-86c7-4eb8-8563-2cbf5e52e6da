// File: lib/features/backup/backup_service.dart
import 'dart:io';
import 'package:csv/csv.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:media_store_plus/media_store_plus.dart';

import '../../core/models/meter_entry.dart';
import '../../core/utils/logger.dart';
import '../../core/utils/permission_helper.dart';
import '../../core/utils/result.dart';
import '../../core/services/platform_service.dart';
import 'backup_errors.dart';

/// Result class for export operations
class ExportResult {
  final bool isSuccess;
  final String? filePath;
  final String? errorMessage;
  final String? tempFilePath;

  const ExportResult._({
    required this.isSuccess,
    this.filePath,
    this.errorMessage,
    this.tempFilePath,
  });

  factory ExportResult.success(String filePath) {
    return ExportResult._(
      isSuccess: true,
      filePath: filePath,
    );
  }

  factory ExportResult.failure(String errorMessage, {String? tempFilePath}) {
    return ExportResult._(
      isSuccess: false,
      errorMessage: errorMessage,
      tempFilePath: tempFilePath,
    );
  }
}

/// Service for backing up and restoring meter entries
class BackupService {
  /// Backup format version
  static const int backupFormatVersion = 101;

  /// Backup filename
  static const String backupFilename = 'lekky_export_101.csv';

  /// Permission helper
  final PermissionHelper _permissionHelper = PermissionHelper();

  /// Logger
  final logger = Logger('BackupService');

  /// Exports meter entries to a CSV file
  ///
  /// Returns a Result with the File on success or an AppError on failure
  /// The optional onComplete callback is called when the export is complete,
  /// regardless of success or failure
  Future<Result<File>> exportMeterEntries({
    required List<MeterEntry> entries,
    VoidCallback? onComplete,
  }) async {
    try {
      // Create CSV data with version header
      final csvData = [
        ['# Lekky v1.0.1 BackupFormat=$backupFormatVersion'],
        ['Date', 'Type', 'Amount', 'Notes'],
        ...entries.map((entry) => [
              // Use ISO 8601 format for better compatibility
              entry.timestamp.toIso8601String(),
              // Use numeric type codes: 0 = Meter Reading, 1 = Top Up, 2 = Records Entry Gap
              entry.typeCode.toString(),
              _getEntryAmount(entry),
              entry.notes ?? '', // Include notes for proper Type=2 detection
            ]),
      ];

      // Convert to CSV string
      final csv = const ListToCsvConverter().convert(csvData);

      // Use Android version-aware export strategy
      final exportResult = await _executeVersionAwareExport(csv);

      // Call the onComplete callback if provided
      onComplete?.call();

      if (exportResult.isSuccess) {
        final file = File(exportResult.filePath!);
        return Result.success(file);
      } else {
        return Result.failure(createBackupError(
          BackupErrorType.fileIOError,
          exportResult.errorMessage ?? 'Export failed',
          details: exportResult.tempFilePath != null
              ? {'tempFilePath': exportResult.tempFilePath}
              : null,
        ));
      }
    } catch (e) {
      logger.e('Failed to export data', details: e.toString());
      // Call the onComplete callback if provided
      onComplete?.call();

      return Result.failure(createBackupError(
        BackupErrorType.fileIOError,
        'Could not save backup file',
        details: e,
      ));
    }
  }

  /// Imports meter entries from a CSV file
  ///
  /// Returns a Result with a list of MeterEntry objects on success or an AppError on failure
  Future<Result<List<MeterEntry>>> importMeterEntries(File csvFile) async {
    try {
      logger.i('Starting import from file: ${csvFile.path}');

      // Verify file exists and get size
      if (!await csvFile.exists()) {
        logger.e('File does not exist: ${csvFile.path}');
        return Result.failure(createBackupError(
          BackupErrorType.fileIOError,
          'File does not exist or is not accessible',
        ));
      }

      final fileSize = await csvFile.length();
      logger.i('File size: $fileSize bytes');

      // Read file content
      final csvString = await csvFile.readAsString();
      logger.i('Read ${csvString.length} characters from file');

      // Check if the file is empty
      if (csvString.trim().isEmpty) {
        logger.w('File content is empty');
        return Result.failure(createBackupError(
          BackupErrorType.emptyData,
          'The backup file is empty',
        ));
      }

      // Split the file into lines for better header parsing
      final lines = csvString.split('\n');
      logger.i('File has ${lines.length} lines');

      if (lines.isEmpty) {
        logger.w('No lines found in file');
        return Result.failure(createBackupError(
          BackupErrorType.emptyData,
          'The backup file is empty',
        ));
      }

      // Log first few lines for debugging (truncated for security)
      final previewLines = lines
          .take(3)
          .map((line) =>
              line.length > 100 ? '${line.substring(0, 100)}...' : line)
          .toList();
      logger.i('First few lines: $previewLines');

      // Check for version header
      final versionHeader = lines.first.trim();
      final versionRegex = RegExp(r'# Lekky v[\d\.]+\s+BackupFormat=(\d+)');
      final versionMatch = versionRegex.firstMatch(versionHeader);

      if (versionMatch == null) {
        return Result.failure(createBackupError(
          BackupErrorType.invalidFormat,
          'Invalid backup file format: missing version header',
        ));
      }

      final backupVersion = int.parse(versionMatch.group(1)!);
      // Allow importing from version 100 (text types) or 101 (numeric types)
      if (backupVersion != backupFormatVersion && backupVersion != 100) {
        return Result.failure(createBackupError(
          BackupErrorType.versionMismatch,
          'This backup was made with an incompatible version of Lekky',
          details: {
            'fileVersion': backupVersion,
            'currentVersion': backupFormatVersion
          },
        ));
      }

      // Convert CSV string to table
      final csvTable = const CsvToListConverter().convert(csvString);
      logger.i('CSV parsed into ${csvTable.length} rows');

      // Validate file format
      if (csvTable.isEmpty || csvTable.length <= 2) {
        logger.w('CSV table has insufficient data: ${csvTable.length} rows');
        if (csvTable.isNotEmpty) {
          logger.i(
              'Available rows: ${csvTable.map((row) => row.toString()).take(3).toList()}');
        }
        return Result.failure(createBackupError(
          BackupErrorType.emptyData,
          'No meter entries found in backup file',
        ));
      }

      // Parse entries
      final entries = <MeterEntry>[];
      int errorCount = 0;

      for (int i = 2; i < csvTable.length; i++) {
        final row = csvTable[i];
        if (row.length >= 3) {
          try {
            final dateStr = row[0].toString();
            final type = row[1].toString();
            final amountStr = row[2].toString();
            final notes = row.length >= 4 ? row[3].toString() : null;

            // Handle special markers for dismissal entries
            final amount = amountStr == '--' ? 0.0 : double.parse(amountStr);

            // Parse the date
            DateTime date;
            try {
              date = DateTime.parse(dateStr);
            } catch (dateError) {
              // Try alternative date formats if ISO 8601 fails
              try {
                // Try dd/MM/yyyy HH:mm format
                date = DateFormat('dd/MM/yyyy HH:mm').parse(dateStr);
              } catch (e) {
                // Try dd-MM-yyyy HH:mm format
                date = DateFormat('dd-MM-yyyy HH:mm').parse(dateStr);
              }
            }

            // Handle both numeric type codes and legacy text types
            MeterEntry entry;

            // Check if type is a numeric code
            if (type == '0' || type == '1' || type == '2') {
              // Use numeric type code (0 = Meter Reading, 1 = Top Up, 2 = Dismissal Entry)
              final typeCode = int.parse(type);
              entry = MeterEntry.fromTypeCodeAndAmount(
                id: null,
                typeCode: typeCode,
                amount: amount,
                timestamp: date,
                notes: notes,
                // No averages - they will be recalculated
                shortAverageAfterTopUp: null,
                totalAverageUpToThisPoint: null,
              );
            } else {
              // Legacy text type format
              entry = MeterEntry(
                id: null,
                reading: type.toLowerCase() == 'meter reading' ? amount : 0,
                amountToppedUp: type.toLowerCase() == 'top-up' ? amount : 0,
                date: date,
                typeCode: type.toLowerCase() == 'meter reading' ? 0 : 1,
                notes: notes,
                // No averages - they will be recalculated
                shortAverageAfterTopUp: null,
                totalAverageUpToThisPoint: null,
              );
            }
            entries.add(entry);
          } catch (e) {
            // Skip invalid rows but log the error with detailed information
            errorCount++;
            logger.e(
              'Error parsing row',
              details: {
                'row': row,
                'dateStr': row[0].toString(),
                'type': row[1].toString(),
                'amount': row[2].toString(),
                'error': e.toString(),
              },
            );
          }
        }
      }

      if (entries.isEmpty) {
        return Result.failure(createBackupError(
          BackupErrorType.parseError,
          'Could not parse any valid entries from the backup file',
        ));
      }

      // Sort entries by timestamp to ensure chronological order
      entries.sort((a, b) => a.timestamp.compareTo(b.timestamp));

      logger.i(
          'Successfully imported ${entries.length} entries (Errors: $errorCount)');
      return Result.success(entries);
    } catch (e) {
      logger.e('Failed to import data', details: e.toString());
      return Result.failure(createBackupError(
        BackupErrorType.unknown,
        'An unexpected error occurred while importing data',
        details: e,
      ));
    }
  }

  /// Picks a backup file using the file picker
  ///
  /// Returns a Result with the File on success or an AppError on failure
  Future<Result<File>> pickBackupFile() async {
    try {
      // Request storage permission for older Android versions before file picking
      if (Platform.isAndroid) {
        final permissionHelper = PermissionHelper();
        final hasPermission =
            await permissionHelper.checkAndRequestStoragePermission();
        if (!hasPermission) {
          return Result.failure(createBackupError(
            BackupErrorType.permission,
            'Storage permission is required to import files',
          ));
        }
      }

      // Use FilePicker to let the user choose a file
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['csv'],
        dialogTitle: 'Select Backup File',
      );

      if (result == null || result.files.isEmpty) {
        return Result.failure(createBackupError(
          BackupErrorType.userCancelled,
          'No file selected',
        ));
      }

      final path = result.files.first.path;
      if (path == null) {
        return Result.failure(createBackupError(
          BackupErrorType.fileIOError,
          'Could not get file path',
        ));
      }

      final file = File(path);
      if (!await file.exists()) {
        return Result.failure(createBackupError(
          BackupErrorType.fileIOError,
          'File does not exist',
        ));
      }

      return Result.success(file);
    } catch (e) {
      logger.e('Failed to pick backup file', details: e.toString());
      return Result.failure(createBackupError(
        BackupErrorType.unknown,
        'An unexpected error occurred while picking a backup file',
        details: e,
      ));
    }
  }

  /// Checks if a backup file exists
  Future<bool> backupFileExists() async {
    try {
      // First try to check using the traditional method
      try {
        // Check if we have permission
        final hasPermission =
            await _permissionHelper.checkAndRequestStoragePermission();
        if (hasPermission) {
          final downloadsPath = await _getDownloadsPath();
          if (downloadsPath != null) {
            final path = '$downloadsPath/$backupFilename';
            final file = File(path);
            final exists = await file.exists();
            if (exists) {
              return true;
            }
          }
        }
      } catch (e) {
        logger.w('Error checking for backup file using traditional method',
            details: e.toString());
      }

      // If traditional method failed, return false
      return false;
    } catch (e) {
      logger.e('Failed to check if backup file exists', details: e.toString());
      return false;
    }
  }

  /// Gets the path to the backup file
  Future<String?> getBackupFilePath() async {
    try {
      // First try to get the path using the traditional method
      try {
        // Check if we have permission
        final hasPermission =
            await _permissionHelper.checkAndRequestStoragePermission();
        if (hasPermission) {
          final downloadsPath = await _getDownloadsPath();
          if (downloadsPath != null) {
            final path = '$downloadsPath/$backupFilename';
            final file = File(path);
            if (await file.exists()) {
              return path;
            }
          }
        }
      } catch (e) {
        logger.w('Error getting backup file path using traditional method',
            details: e.toString());
      }

      // If traditional method failed, return null
      return null;
    } catch (e) {
      logger.e('Failed to get backup file path', details: e.toString());
      return null;
    }
  }

  /// Gets the path to the Downloads folder (WIP51 proven approach)
  Future<String?> _getDownloadsPath() async {
    try {
      if (Platform.isAndroid) {
        final directory = await getExternalStorageDirectory();
        if (directory != null) {
          logger.i(
              '🔄 _getDownloadsPath: External storage directory: ${directory.path}');

          // Navigate up to the external storage root (WIP51 approach)
          final externalDir = directory.path.split('/Android')[0];
          final downloadsDir = '$externalDir/Download';

          logger.i('🔄 _getDownloadsPath: Downloads directory: $downloadsDir');

          // Create the directory if it doesn't exist (WIP51 approach)
          final dir = Directory(downloadsDir);
          if (!await dir.exists()) {
            logger.i('🔄 _getDownloadsPath: Creating Downloads directory');
            await dir.create(recursive: true);
          }

          logger.i(
              '🔄 _getDownloadsPath: SUCCESS - Downloads folder confirmed: $downloadsDir');
          return downloadsDir;
        } else {
          logger.w('🔄 _getDownloadsPath: External storage directory is null');
        }
      }

      // For iOS and fallback
      logger.i(
          '🔄 _getDownloadsPath: Using iOS/fallback - app documents directory');
      final directory = await getApplicationDocumentsDirectory();
      return directory.path;
    } catch (e) {
      logger.e('🔄 _getDownloadsPath: ERROR - $e');
      return null;
    }
  }

  /// Get the appropriate amount string for CSV export based on entry type
  String _getEntryAmount(MeterEntry entry) {
    switch (entry.typeCode) {
      case 0: // Meter Reading
        return entry.reading.toString();
      case 1: // Top-up
        return entry.amountToppedUp.toString();
      case 2: // Records Entry Gap
        return '--'; // Special marker for dismissal entries
      default:
        return '0';
    }
  }

  /// Execute version-aware export strategy based on platform and Android version
  Future<ExportResult> _executeVersionAwareExport(String csv) async {
    try {
      final platformInfo = await PlatformService.getPlatformInfo();
      logger.i('Platform detected: ${platformInfo.userFriendlyDescription}');

      if (platformInfo.isIOS) {
        logger.i('iOS detected - using Storage Access Framework');
        final filePath = await _tryStorageAccessFramework(csv);
        if (filePath != null) {
          return ExportResult.success(filePath);
        }
        return ExportResult.failure(
            'Could not save file on iOS. Please try again.');
      }

      if (platformInfo.isAndroid && platformInfo.androidSdkInt != null) {
        final sdkInt = platformInfo.androidSdkInt!;
        logger.i('Android API level detected: $sdkInt');

        // For Android 15+ (API 35+): Skip media_store_plus due to subdirectory issues
        if (sdkInt >= 35) {
          logger.i(
              'Android 15+ detected - skipping media_store_plus, using traditional method');

          // Try traditional method first (saves directly to Downloads root)
          String? filePath = await _tryTraditionalMethod(csv);
          if (filePath != null) {
            return ExportResult.success(filePath);
          }

          // Try Storage Access Framework as fallback
          logger
              .i('Traditional method failed - trying Storage Access Framework');
          filePath = await _tryStorageAccessFramework(csv);
          if (filePath != null) {
            return ExportResult.success(filePath);
          }

          // Final fallback to app-specific storage
          logger.w('All methods failed - creating temp file for sharing');
          final tempPath = await _createTempFile(csv);
          return ExportResult.failure(
            'Could not save to Downloads folder. File saved to app storage.',
            tempFilePath: tempPath,
          );
        }

        // For Android 14 (API 34): Use media_store_plus as primary method
        else if (sdkInt >= 34) {
          logger.i('Android 14 detected - using media_store_plus');

          String? filePath = await _tryMediaStorePlus(csv);
          if (filePath != null) {
            return ExportResult.success(filePath);
          }

          // Fallback to traditional method
          logger.i('MediaStore failed - trying traditional method');
          filePath = await _tryTraditionalMethod(csv);
          if (filePath != null) {
            return ExportResult.success(filePath);
          }

          // Final fallback to SAF
          logger
              .i('Traditional method failed - trying Storage Access Framework');
          filePath = await _tryStorageAccessFramework(csv);
          if (filePath != null) {
            return ExportResult.success(filePath);
          }

          // Final fallback to app-specific storage
          logger.w('All methods failed - creating temp file for sharing');
          final tempPath = await _createTempFile(csv);
          return ExportResult.failure(
            'Could not save to Downloads folder. File saved to app storage.',
            tempFilePath: tempPath,
          );
        }

        // For Android 10-13 (API 29-33): Prioritize Downloads folder with media_store_plus fallback
        else if (sdkInt >= 29) {
          logger.i('Android 10-13 detected - trying Downloads folder first');

          // Try traditional Downloads folder access first
          String? filePath = await _tryTraditionalMethod(csv);
          if (filePath != null) {
            return ExportResult.success(filePath);
          }

          // Fallback to media_store_plus
          logger.i('Downloads folder failed - trying media_store_plus');
          filePath = await _tryMediaStorePlus(csv);
          if (filePath != null) {
            return ExportResult.success(filePath);
          }

          // Final fallback to SAF
          logger.i('MediaStore failed - trying Storage Access Framework');
          filePath = await _tryStorageAccessFramework(csv);
          if (filePath != null) {
            return ExportResult.success(filePath);
          }

          // Final fallback to app-specific storage
          logger.w('All methods failed - creating temp file for sharing');
          final tempPath = await _createTempFile(csv);
          return ExportResult.failure(
            'Could not save to Downloads folder. File saved to app storage.',
            tempFilePath: tempPath,
          );
        }

        // For Android 5-9 (API 21-28): Prioritize traditional method
        else if (sdkInt >= 21) {
          logger.i('Android 5.0-9.0 detected - using traditional file access');

          // Try traditional method first (more reliable on older Android)
          String? filePath = await _tryTraditionalMethod(csv);
          if (filePath != null) {
            return ExportResult.success(filePath);
          }

          // Try SAF as fallback (limited support on older versions)
          logger
              .i('Traditional method failed - trying Storage Access Framework');
          filePath = await _tryStorageAccessFramework(csv);
          if (filePath != null) {
            return ExportResult.success(filePath);
          }

          // Final fallback to app-specific storage
          logger.w('All methods failed - creating temp file for sharing');
          final tempPath = await _createTempFile(csv);
          return ExportResult.failure(
            'Could not save to Downloads folder. File saved to app storage.',
            tempFilePath: tempPath,
          );
        }

        // For very old Android versions (below API 21)
        else {
          logger.w(
              'Very old Android version detected (API $sdkInt) - limited export options');
          final tempPath = await _createTempFile(csv);
          return ExportResult.failure(
            'Your Android version has limited file access. File saved to app storage.',
            tempFilePath: tempPath,
          );
        }
      }

      // Fallback for unknown platforms
      logger.w('Unknown platform detected - using fallback method');
      final tempPath = await _createTempFile(csv);
      return ExportResult.failure(
        'Platform not fully supported. File saved to temporary storage.',
        tempFilePath: tempPath,
      );
    } catch (e) {
      logger.e('Error in version-aware export: $e');
      final tempPath = await _createTempFile(csv);
      return ExportResult.failure(
        'Export failed: $e',
        tempFilePath: tempPath,
      );
    }
  }

  /// Try Storage Access Framework method
  Future<String?> _tryStorageAccessFramework(String csv) async {
    try {
      logger.i('Attempting Storage Access Framework export...');

      // Use FilePicker to let the user choose where to save the file with timeout
      final result = await FilePicker.platform.saveFile(
        dialogTitle: 'Save Backup File',
        fileName: backupFilename,
        type: FileType.custom,
        allowedExtensions: ['csv'],
      ).timeout(
        const Duration(seconds: 60),
        onTimeout: () {
          logger.w('Storage Access Framework timed out after 60 seconds');
          return null;
        },
      );

      if (result != null) {
        // User selected a location - write file with timeout
        final file = File(result);
        await file.writeAsString(csv).timeout(
          const Duration(seconds: 30),
          onTimeout: () {
            logger.w('File write operation timed out');
            throw Exception('File write timeout');
          },
        );
        logger.i('Data exported using Storage Access Framework: $result');
        return result;
      } else {
        logger.i('User cancelled file selection or operation timed out');
      }
    } catch (e) {
      logger.w('Storage Access Framework failed: $e');
    }
    return null;
  }

  /// Try traditional method with permissions (WIP51 proven approach)
  Future<String?> _tryTraditionalMethod(String csv) async {
    try {
      logger.i('🔄 Traditional method: Starting (WIP51 proven approach)');

      final hasPermission =
          await _permissionHelper.checkAndRequestStoragePermission();
      if (hasPermission) {
        logger.i('🔄 Traditional method: Storage permission granted');

        final downloadsPath = await _getDownloadsPath();
        if (downloadsPath != null) {
          // Create file with timestamp (like WIP51 but with timestamp for uniqueness)
          final timestamp = DateTime.now().millisecondsSinceEpoch;
          final fileName = 'lekky_export_$timestamp.csv';
          final path = '$downloadsPath/$fileName';
          final file = File(path);

          logger.i('🔄 Traditional method: Writing to file: $path');
          await file.writeAsString(csv);

          logger.i('🔄 Traditional method: SUCCESS - File saved: $path');
          return path;
        } else {
          logger.w('🔄 Traditional method: Downloads path is null');
        }
      } else {
        logger.w('🔄 Traditional method: Storage permission denied');
      }
    } catch (e) {
      logger.e('🔄 Traditional method: FAILED - $e');
    }
    return null;
  }

  /// Try media_store_plus method for Android 10+ file access
  Future<String?> _tryMediaStorePlus(String csv) async {
    try {
      logger.i('Attempting media_store_plus export...');

      // Create temporary file
      final tempDir = await getTemporaryDirectory();
      final tempFile = File('${tempDir.path}/$backupFilename');
      await tempFile.writeAsString(csv);

      // Initialize MediaStore and set app folder
      await MediaStore.ensureInitialized();
      MediaStore.appFolder = "Lekky";

      // Save to Downloads folder using MediaStore API
      final mediaStore = MediaStore();
      final saveInfo = await mediaStore.saveFile(
        tempFilePath: tempFile.path,
        dirType: DirType.download,
        dirName: DirName.download,
      );

      // Clean up temporary file
      if (await tempFile.exists()) {
        await tempFile.delete();
      }

      if (saveInfo != null) {
        final filePath = saveInfo.uri.toString();
        if (filePath.isNotEmpty) {
          logger.i('Data exported using media_store_plus: $filePath');
          return filePath;
        }
      }
    } catch (e) {
      logger.w('MediaStore method failed: $e');
    }
    return null;
  }

  /// Create temporary file as fallback
  Future<String> _createTempFile(String csv) async {
    final tempDir = await getTemporaryDirectory();
    final tempFile = File('${tempDir.path}/$backupFilename');
    await tempFile.writeAsString(csv);
    return tempFile.path;
  }
}
