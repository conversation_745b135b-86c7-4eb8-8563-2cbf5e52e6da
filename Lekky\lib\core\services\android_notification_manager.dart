import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:permission_handler/permission_handler.dart';
import '../utils/logger.dart';
import '../../features/notifications/domain/models/notification.dart';

/// Android-specific notification management and optimizations
class AndroidNotificationManager {
  static final AndroidNotificationManager _instance =
      AndroidNotificationManager._internal();

  factory AndroidNotificationManager() => _instance;
  AndroidNotificationManager._internal();

  final FlutterLocalNotificationsPlugin _notificationsPlugin =
      FlutterLocalNotificationsPlugin();

  /// Initialize Android-specific notification features
  Future<bool> initialize() async {
    if (!Platform.isAndroid) return true;

    try {
      Logger.info('AndroidNotificationManager: Initializing');

      // Create enhanced notification channels
      await _createEnhancedNotificationChannels();

      // Check and request additional permissions
      await _checkAndroidSpecificPermissions();

      Logger.info('AndroidNotificationManager: Initialization complete');
      return true;
    } catch (e) {
      Logger.error('AndroidNotificationManager: Initialization failed: $e');
      return false;
    }
  }

  /// Create enhanced notification channels with high priority settings
  Future<void> _createEnhancedNotificationChannels() async {
    try {
      final androidImplementation =
          _notificationsPlugin.resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>();

      if (androidImplementation == null) return;

      // Critical alerts channel with maximum priority
      const AndroidNotificationChannel criticalChannel =
          AndroidNotificationChannel(
        'lekky_critical_alerts_enhanced',
        'Critical Alerts (Enhanced)',
        description: 'High priority alerts for critical meter conditions',
        importance: Importance.max,
        enableVibration: true,
        enableLights: true,
        playSound: true,
        showBadge: true,
        ledColor: Color(0xFF4A90E2),
      );

      // Reminder channel with high priority
      const AndroidNotificationChannel reminderChannel =
          AndroidNotificationChannel(
        'lekky_reminders_enhanced',
        'Meter Reading Reminders (Enhanced)',
        description: 'Enhanced reminders for meter readings',
        importance: Importance.high,
        enableVibration: true,
        enableLights: true,
        playSound: true,
        showBadge: true,
        ledColor: Color(0xFF7E57C2),
      );

      // Threshold alerts channel
      const AndroidNotificationChannel thresholdChannel =
          AndroidNotificationChannel(
        'lekky_threshold_alerts_enhanced',
        'Threshold Alerts (Enhanced)',
        description: 'Enhanced alerts when approaching thresholds',
        importance: Importance.high,
        enableVibration: true,
        enableLights: true,
        playSound: true,
        showBadge: true,
        ledColor: Color(0xFFFF9999),
      );

      // Create all channels
      await androidImplementation.createNotificationChannel(criticalChannel);
      await androidImplementation.createNotificationChannel(reminderChannel);
      await androidImplementation.createNotificationChannel(thresholdChannel);

      Logger.info('Enhanced Android notification channels created');
    } catch (e) {
      Logger.error('Failed to create enhanced notification channels: $e');
    }
  }

  /// Check Android-specific permissions for enhanced notifications
  Future<void> _checkAndroidSpecificPermissions() async {
    try {
      // Check exact alarm permission (Android 12+)
      final exactAlarmStatus = await Permission.scheduleExactAlarm.status;
      Logger.info('Exact alarm permission: $exactAlarmStatus');

      // Check notification permission (Android 13+)
      final notificationStatus = await Permission.notification.status;
      Logger.info('Notification permission: $notificationStatus');

      // Check battery optimization status
      final batteryOptimizationStatus =
          await Permission.ignoreBatteryOptimizations.status;
      Logger.info('Battery optimization exemption: $batteryOptimizationStatus');

      // Log overall permission status
      final allGranted = exactAlarmStatus.isGranted &&
          notificationStatus.isGranted &&
          batteryOptimizationStatus.isGranted;

      Logger.info('All Android permissions granted: $allGranted');
    } catch (e) {
      Logger.error('Error checking Android-specific permissions: $e');
    }
  }

  /// Get enhanced Android notification details for specific notification type
  AndroidNotificationDetails getEnhancedNotificationDetails(
      NotificationType type) {
    switch (type) {
      case NotificationType.lowBalance:
        return const AndroidNotificationDetails(
          'lekky_critical_alerts_enhanced',
          'Critical Alerts (Enhanced)',
          channelDescription:
              'High priority alerts for critical meter conditions',
          importance: Importance.max,
          priority: Priority.max,
          icon: '@mipmap/ic_launcher',
          enableVibration: true,
          enableLights: true,
          playSound: true,
          autoCancel: false,
          ongoing: false,
          showWhen: true,
          when: null,
          usesChronometer: false,
          fullScreenIntent: true,
          category: AndroidNotificationCategory.alarm,
          visibility: NotificationVisibility.public,
          timeoutAfter: null,
          styleInformation: BigTextStyleInformation(''),
        );

      case NotificationType.readingReminder:
        return const AndroidNotificationDetails(
          'lekky_reminders_enhanced',
          'Meter Reading Reminders (Enhanced)',
          channelDescription: 'Enhanced reminders for meter readings',
          importance: Importance.high,
          priority: Priority.high,
          icon: '@mipmap/ic_launcher',
          enableVibration: true,
          enableLights: true,
          playSound: true,
          autoCancel: true,
          ongoing: false,
          showWhen: true,
          category: AndroidNotificationCategory.reminder,
          visibility: NotificationVisibility.public,
          styleInformation: BigTextStyleInformation(''),
        );

      case NotificationType.timeToTopUp:
      case NotificationType.invalidRecord:
        return const AndroidNotificationDetails(
          'lekky_threshold_alerts_enhanced',
          'Threshold Alerts (Enhanced)',
          channelDescription: 'Enhanced alerts when approaching thresholds',
          importance: Importance.high,
          priority: Priority.high,
          icon: '@mipmap/ic_launcher',
          enableVibration: true,
          enableLights: true,
          playSound: true,
          autoCancel: true,
          ongoing: false,
          showWhen: true,
          category: AndroidNotificationCategory.status,
          visibility: NotificationVisibility.public,
          styleInformation: BigTextStyleInformation(''),
        );

      case NotificationType.welcome:
      case NotificationType.appUpdate:
        return const AndroidNotificationDetails(
          'lekky_threshold_alerts_enhanced',
          'Threshold Alerts (Enhanced)',
          channelDescription: 'Enhanced alerts when approaching thresholds',
          importance: Importance.defaultImportance,
          priority: Priority.defaultPriority,
          icon: '@mipmap/ic_launcher',
          enableVibration: false,
          enableLights: false,
          playSound: true,
          autoCancel: true,
          ongoing: false,
          showWhen: true,
          category: AndroidNotificationCategory.status,
          visibility: NotificationVisibility.public,
        );
    }
  }

  /// Request all Android-specific permissions
  Future<bool> requestAllAndroidPermissions(BuildContext context) async {
    if (!Platform.isAndroid) return true;

    try {
      // Request notification permission
      final notificationGranted = await Permission.notification.request();

      // Request exact alarm permission
      final exactAlarmGranted = await Permission.scheduleExactAlarm.request();

      // Request battery optimization exemption
      final batteryOptimizationGranted =
          await Permission.ignoreBatteryOptimizations.request();

      final allGranted = notificationGranted.isGranted &&
          exactAlarmGranted.isGranted &&
          batteryOptimizationGranted.isGranted;

      Logger.info('Android permissions request result: $allGranted');
      return allGranted;
    } catch (e) {
      Logger.error('Error requesting Android permissions: $e');
      return false;
    }
  }

  /// Get Android notification system status
  Future<Map<String, dynamic>> getAndroidNotificationStatus() async {
    if (!Platform.isAndroid) return {'platform': 'not_android'};

    final status = <String, dynamic>{};

    try {
      status['platform'] = 'android';
      status['notificationPermission'] =
          (await Permission.notification.status).toString();
      status['exactAlarmPermission'] =
          (await Permission.scheduleExactAlarm.status).toString();
      status['batteryOptimizationExemption'] =
          (await Permission.ignoreBatteryOptimizations.status).toString();
      status['timestamp'] = DateTime.now().toIso8601String();
    } catch (e) {
      Logger.error('Error getting Android notification status: $e');
      status['error'] = e.toString();
    }

    return status;
  }

  /// Manages foreground notifications to prevent Doze mode interference
  Future<void> setupForegroundService() async {
    if (!Platform.isAndroid) return;

    try {
      Logger.info('AndroidNotificationManager: Setting up foreground service');

      // Create a persistent notification channel for foreground service
      await _createForegroundServiceChannel();

      // Start foreground service notification
      await _startForegroundServiceNotification();

      Logger.info(
          'AndroidNotificationManager: Foreground service setup complete');
    } catch (e) {
      Logger.error('Failed to setup foreground service: $e');
    }
  }

  /// Create notification channel for foreground service
  Future<void> _createForegroundServiceChannel() async {
    try {
      final androidImplementation =
          _notificationsPlugin.resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>();

      if (androidImplementation == null) return;

      const AndroidNotificationChannel foregroundChannel =
          AndroidNotificationChannel(
        'lekky_foreground_service',
        'Background Monitoring',
        description: 'Background monitoring service',
        importance:
            Importance.min, // Minimal importance for invisible operation
        enableVibration: false,
        enableLights: false,
        playSound: false,
        showBadge: false,
      );

      await androidImplementation.createNotificationChannel(foregroundChannel);
      Logger.info('Foreground service notification channel created');
    } catch (e) {
      Logger.error('Failed to create foreground service channel: $e');
    }
  }

  /// Start foreground service notification
  Future<void> _startForegroundServiceNotification() async {
    try {
      const AndroidNotificationDetails androidDetails =
          AndroidNotificationDetails(
        'lekky_foreground_service',
        'Background Monitoring',
        channelDescription: 'Background monitoring service',
        importance: Importance.min,
        priority: Priority.min,
        icon: '@mipmap/ic_launcher',
        enableVibration: false,
        enableLights: false,
        playSound: false,
        autoCancel: false,
        ongoing: true, // Makes it persistent
        showWhen: false,
        category: AndroidNotificationCategory.service,
        visibility: NotificationVisibility.secret,
      );

      const NotificationDetails notificationDetails =
          NotificationDetails(android: androidDetails);

      await _notificationsPlugin.show(
        999999, // Use a high ID to avoid conflicts
        '',
        '',
        notificationDetails,
      );

      Logger.info('Foreground service notification started');
    } catch (e) {
      Logger.error('Failed to start foreground service notification: $e');
    }
  }

  /// Stop foreground service notification
  Future<void> stopForegroundService() async {
    if (!Platform.isAndroid) return;

    try {
      await _notificationsPlugin.cancel(999999);
      Logger.info('Foreground service notification stopped');
    } catch (e) {
      Logger.error('Failed to stop foreground service notification: $e');
    }
  }

  /// Ensures exact alarm permissions are granted for critical notifications
  Future<bool> requestExactAlarmPermission() async {
    if (!Platform.isAndroid) return true;

    try {
      final status = await Permission.scheduleExactAlarm.status;
      Logger.info('Current exact alarm permission status: $status');

      if (!status.isGranted) {
        Logger.info('Requesting exact alarm permission');
        final result = await Permission.scheduleExactAlarm.request();
        Logger.info('Exact alarm permission request result: $result');
        return result.isGranted;
      }

      return true;
    } catch (e) {
      Logger.error('Failed to request exact alarm permission: $e');
      return false;
    }
  }

  /// Check if device is in Doze mode or has battery optimization enabled
  Future<Map<String, bool>> checkBatteryOptimizationStatus() async {
    if (!Platform.isAndroid) {
      return {'dozeMode': false, 'batteryOptimized': false};
    }

    final status = <String, bool>{};

    try {
      // Check battery optimization status
      final batteryOptimizationStatus =
          await Permission.ignoreBatteryOptimizations.status;
      status['batteryOptimized'] = !batteryOptimizationStatus.isGranted;

      // Note: Doze mode detection requires platform-specific code
      // For now, we'll assume it's active if battery optimization is enabled
      status['dozeMode'] = !batteryOptimizationStatus.isGranted;

      Logger.info('Battery optimization status: $status');
    } catch (e) {
      Logger.error('Error checking battery optimization status: $e');
      status['dozeMode'] = false;
      status['batteryOptimized'] = false;
    }

    return status;
  }

  /// Request battery optimization exemption
  Future<bool> requestBatteryOptimizationExemption() async {
    if (!Platform.isAndroid) return true;

    try {
      final status = await Permission.ignoreBatteryOptimizations.status;
      Logger.info('Current battery optimization exemption status: $status');

      if (!status.isGranted) {
        Logger.info('Requesting battery optimization exemption');
        final result = await Permission.ignoreBatteryOptimizations.request();
        Logger.info('Battery optimization exemption request result: $result');
        return result.isGranted;
      }

      return true;
    } catch (e) {
      Logger.error('Failed to request battery optimization exemption: $e');
      return false;
    }
  }
}
