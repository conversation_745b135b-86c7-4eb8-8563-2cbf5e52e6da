import 'package:flutter/material.dart';
import '../../../../core/shared/widgets/section_header.dart';

/// A widget for section headers in the setup screen
class SetupSectionHeader extends StatelessWidget {
  /// Title of the section
  final String title;

  /// Description of the section
  final String? description;

  /// Icon to display
  final IconData? icon;

  /// Constructor
  const SetupSectionHeader({
    super.key,
    required this.title,
    this.description,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return SectionHeader(
      title: title,
      description: description,
      icon: icon,
      style: SectionHeaderStyle.setup,
    );
  }
}
