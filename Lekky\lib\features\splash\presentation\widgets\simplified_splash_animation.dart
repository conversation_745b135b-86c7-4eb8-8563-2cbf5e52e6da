// File: lib/features/splash/presentation/widgets/simplified_splash_animation.dart
import 'dart:math';
import 'package:flutter/material.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// A widget that displays an animated splash screen
class SimplifiedSplashAnimation extends StatefulWidget {
  const SimplifiedSplashAnimation({super.key});

  @override
  State<SimplifiedSplashAnimation> createState() =>
      _SimplifiedSplashAnimationState();
}

class _SimplifiedSplashAnimationState extends State<SimplifiedSplashAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _opacityAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotateAnimation;

  // Randomly selected joke index
  late int _selectedJokeIndex;

  @override
  void initState() {
    super.initState();
    // Select a random joke index (1-10)
    _selectedJokeIndex = Random().nextInt(10) + 1;
    _setupAnimations();
  }

  void _setupAnimations() {
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    _opacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 0.5, curve: Curves.easeIn),
      ),
    );

    _scaleAnimation = Tween<double>(begin: 0.5, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 0.5, curve: Curves.easeOut),
      ),
    );

    _rotateAnimation = Tween<double>(begin: 0.0, end: 0.05).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.5, 1.0, curve: Curves.elasticInOut),
      ),
    );

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Opacity(
          opacity: _opacityAnimation.value,
          child: Transform.scale(
            scale: _scaleAnimation.value,
            child: Transform.rotate(
              angle: _rotateAnimation.value,
              child: child,
            ),
          ),
        );
      },
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.electric_meter,
            size: 100,
            color: Colors.white,
          ),
          const SizedBox(height: 24),
          Text(
            AppLocalizations.of(context).appName,
            style: const TextStyle(
              fontSize: 48,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            AppLocalizations.of(context).tagline,
            style: const TextStyle(
              fontSize: 18,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 32),
          // Display the joke
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 32),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.15),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              _getLocalizedQuote(context),
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.white,
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
          const SizedBox(height: 32),
          const CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
          ),
        ],
      ),
    );
  }

  /// Get localized quote based on selected index
  String _getLocalizedQuote(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    switch (_selectedJokeIndex) {
      case 1:
        return localizations.splashQuote1;
      case 2:
        return localizations.splashQuote2;
      case 3:
        return localizations.splashQuote3;
      case 4:
        return localizations.splashQuote4;
      case 5:
        return localizations.splashQuote5;
      case 6:
        return localizations.splashQuote6;
      case 7:
        return localizations.splashQuote7;
      case 8:
        return localizations.splashQuote8;
      case 9:
        return localizations.splashQuote9;
      case 10:
        return localizations.splashQuote10;
      default:
        return localizations.splashQuote1;
    }
  }
}
