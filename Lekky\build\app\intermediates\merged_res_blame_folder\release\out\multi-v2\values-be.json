{"logs": [{"outputFile": "com.lekky.app-mergeReleaseResources-24:/values-be/values-be.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4f3949bbe11b9a3df8476b3579cc79b0\\transformed\\browser-1.5.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,164,272,384", "endColumns": "108,107,111,106", "endOffsets": "159,267,379,486"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3018,3127,3235,3347", "endColumns": "108,107,111,106", "endOffsets": "3122,3230,3342,3449"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\684577351670909f117ab3c5c378ca3b\\transformed\\core-1.10.1\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,562,665,786", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "148,250,350,451,557,660,781,882"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,562,665,3454", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "148,250,350,451,557,660,781,3550"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\473c6549ebdad33ab68cbd325533c84f\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-be\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1790", "endColumns": "145", "endOffsets": "1931"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\33a23a1921cc9e261ad760b2275ca606\\transformed\\jetified-play-services-base-18.0.1\\res\\values-be\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,454,575,681,832,954,1065,1165,1323,1426,1585,1709,1858,2013,2078,2136", "endColumns": "102,157,120,105,150,121,110,99,157,102,158,123,148,154,64,57,74", "endOffsets": "295,453,574,680,831,953,1064,1164,1322,1425,1584,1708,1857,2012,2077,2135,2210"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "786,893,1055,1180,1290,1445,1571,1686,1936,2098,2205,2368,2496,2649,2808,2877,2939", "endColumns": "106,161,124,109,154,125,114,103,161,106,162,127,152,158,68,61,78", "endOffsets": "888,1050,1175,1285,1440,1566,1681,1785,2093,2200,2363,2491,2644,2803,2872,2934,3013"}}]}]}