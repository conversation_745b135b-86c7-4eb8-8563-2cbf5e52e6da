int attr activityAction 0x7f010000
int attr activityName 0x7f010001
int attr alpha 0x7f010002
int attr alwaysExpand 0x7f010003
int attr buttonSize 0x7f010004
int attr circleCrop 0x7f010005
int attr clearTop 0x7f010006
int attr colorScheme 0x7f010007
int attr finishPrimaryWithSecondary 0x7f010008
int attr finishSecondaryWithPrimary 0x7f010009
int attr font 0x7f01000a
int attr fontProviderAuthority 0x7f01000b
int attr fontProviderCerts 0x7f01000c
int attr fontProviderFetchStrategy 0x7f01000d
int attr fontProviderFetchTimeout 0x7f01000e
int attr fontProviderPackage 0x7f01000f
int attr fontProviderQuery 0x7f010010
int attr fontProviderSystemFontFamily 0x7f010011
int attr fontStyle 0x7f010012
int attr fontVariationSettings 0x7f010013
int attr fontWeight 0x7f010014
int attr imageAspectRatio 0x7f010015
int attr imageAspectRatioAdjust 0x7f010016
int attr lStar 0x7f010017
int attr nestedScrollViewStyle 0x7f010018
int attr placeholderActivityName 0x7f010019
int attr primaryActivityName 0x7f01001a
int attr queryPatterns 0x7f01001b
int attr scopeUris 0x7f01001c
int attr secondaryActivityAction 0x7f01001d
int attr secondaryActivityName 0x7f01001e
int attr shortcutMatchRequired 0x7f01001f
int attr splitLayoutDirection 0x7f010020
int attr splitMinSmallestWidth 0x7f010021
int attr splitMinWidth 0x7f010022
int attr splitRatio 0x7f010023
int attr ttcIndex 0x7f010024
int bool enable_system_alarm_service_default 0x7f020000
int bool enable_system_foreground_service_default 0x7f020001
int bool enable_system_job_service_default 0x7f020002
int bool workmanager_test_configuration 0x7f020003
int color androidx_core_ripple_material_light 0x7f030000
int color androidx_core_secondary_text_default_material_light 0x7f030001
int color browser_actions_bg_grey 0x7f030002
int color browser_actions_divider_color 0x7f030003
int color browser_actions_text_color 0x7f030004
int color browser_actions_title_color 0x7f030005
int color call_notification_answer_color 0x7f030006
int color call_notification_decline_color 0x7f030007
int color common_google_signin_btn_text_dark 0x7f030008
int color common_google_signin_btn_text_dark_default 0x7f030009
int color common_google_signin_btn_text_dark_disabled 0x7f03000a
int color common_google_signin_btn_text_dark_focused 0x7f03000b
int color common_google_signin_btn_text_dark_pressed 0x7f03000c
int color common_google_signin_btn_text_light 0x7f03000d
int color common_google_signin_btn_text_light_default 0x7f03000e
int color common_google_signin_btn_text_light_disabled 0x7f03000f
int color common_google_signin_btn_text_light_focused 0x7f030010
int color common_google_signin_btn_text_light_pressed 0x7f030011
int color common_google_signin_btn_tint 0x7f030012
int color notification_action_color_filter 0x7f030013
int color notification_icon_bg_color 0x7f030014
int color notification_material_background_media_default_color 0x7f030015
int color primary_text_default_material_dark 0x7f030016
int color secondary_text_default_material_dark 0x7f030017
int dimen browser_actions_context_menu_max_width 0x7f040000
int dimen browser_actions_context_menu_min_padding 0x7f040001
int dimen compat_button_inset_horizontal_material 0x7f040002
int dimen compat_button_inset_vertical_material 0x7f040003
int dimen compat_button_padding_horizontal_material 0x7f040004
int dimen compat_button_padding_vertical_material 0x7f040005
int dimen compat_control_corner_material 0x7f040006
int dimen compat_notification_large_icon_max_height 0x7f040007
int dimen compat_notification_large_icon_max_width 0x7f040008
int dimen notification_action_icon_size 0x7f040009
int dimen notification_action_text_size 0x7f04000a
int dimen notification_big_circle_margin 0x7f04000b
int dimen notification_content_margin_start 0x7f04000c
int dimen notification_large_icon_height 0x7f04000d
int dimen notification_large_icon_width 0x7f04000e
int dimen notification_main_column_padding_top 0x7f04000f
int dimen notification_media_narrow_margin 0x7f040010
int dimen notification_right_icon_size 0x7f040011
int dimen notification_right_side_padding_top 0x7f040012
int dimen notification_small_icon_background_padding 0x7f040013
int dimen notification_small_icon_size_as_large 0x7f040014
int dimen notification_subtext_size 0x7f040015
int dimen notification_top_pad 0x7f040016
int dimen notification_top_pad_large_text 0x7f040017
int dimen subtitle_corner_radius 0x7f040018
int dimen subtitle_outline_width 0x7f040019
int dimen subtitle_shadow_offset 0x7f04001a
int dimen subtitle_shadow_radius 0x7f04001b
int drawable common_full_open_on_phone 0x7f050000
int drawable common_google_signin_btn_icon_dark 0x7f050001
int drawable common_google_signin_btn_icon_dark_focused 0x7f050002
int drawable common_google_signin_btn_icon_dark_normal 0x7f050003
int drawable common_google_signin_btn_icon_dark_normal_background 0x7f050004
int drawable common_google_signin_btn_icon_disabled 0x7f050005
int drawable common_google_signin_btn_icon_light 0x7f050006
int drawable common_google_signin_btn_icon_light_focused 0x7f050007
int drawable common_google_signin_btn_icon_light_normal 0x7f050008
int drawable common_google_signin_btn_icon_light_normal_background 0x7f050009
int drawable common_google_signin_btn_text_dark 0x7f05000a
int drawable common_google_signin_btn_text_dark_focused 0x7f05000b
int drawable common_google_signin_btn_text_dark_normal 0x7f05000c
int drawable common_google_signin_btn_text_dark_normal_background 0x7f05000d
int drawable common_google_signin_btn_text_disabled 0x7f05000e
int drawable common_google_signin_btn_text_light 0x7f05000f
int drawable common_google_signin_btn_text_light_focused 0x7f050010
int drawable common_google_signin_btn_text_light_normal 0x7f050011
int drawable common_google_signin_btn_text_light_normal_background 0x7f050012
int drawable googleg_disabled_color_18 0x7f050013
int drawable googleg_standard_color_18 0x7f050014
int drawable ic_call_answer 0x7f050015
int drawable ic_call_answer_low 0x7f050016
int drawable ic_call_answer_video 0x7f050017
int drawable ic_call_answer_video_low 0x7f050018
int drawable ic_call_decline 0x7f050019
int drawable ic_call_decline_low 0x7f05001a
int drawable launch_background 0x7f05001b
int drawable notification_action_background 0x7f05001c
int drawable notification_bg 0x7f05001d
int drawable notification_bg_low 0x7f05001e
int drawable notification_bg_low_normal 0x7f05001f
int drawable notification_bg_low_pressed 0x7f050020
int drawable notification_bg_normal 0x7f050021
int drawable notification_bg_normal_pressed 0x7f050022
int drawable notification_icon_background 0x7f050023
int drawable notification_template_icon_bg 0x7f050024
int drawable notification_template_icon_low_bg 0x7f050025
int drawable notification_tile_bg 0x7f050026
int drawable notify_panel_notification_icon_bg 0x7f050027
int id accessibility_action_clickable_span 0x7f060000
int id accessibility_custom_action_0 0x7f060001
int id accessibility_custom_action_1 0x7f060002
int id accessibility_custom_action_10 0x7f060003
int id accessibility_custom_action_11 0x7f060004
int id accessibility_custom_action_12 0x7f060005
int id accessibility_custom_action_13 0x7f060006
int id accessibility_custom_action_14 0x7f060007
int id accessibility_custom_action_15 0x7f060008
int id accessibility_custom_action_16 0x7f060009
int id accessibility_custom_action_17 0x7f06000a
int id accessibility_custom_action_18 0x7f06000b
int id accessibility_custom_action_19 0x7f06000c
int id accessibility_custom_action_2 0x7f06000d
int id accessibility_custom_action_20 0x7f06000e
int id accessibility_custom_action_21 0x7f06000f
int id accessibility_custom_action_22 0x7f060010
int id accessibility_custom_action_23 0x7f060011
int id accessibility_custom_action_24 0x7f060012
int id accessibility_custom_action_25 0x7f060013
int id accessibility_custom_action_26 0x7f060014
int id accessibility_custom_action_27 0x7f060015
int id accessibility_custom_action_28 0x7f060016
int id accessibility_custom_action_29 0x7f060017
int id accessibility_custom_action_3 0x7f060018
int id accessibility_custom_action_30 0x7f060019
int id accessibility_custom_action_31 0x7f06001a
int id accessibility_custom_action_4 0x7f06001b
int id accessibility_custom_action_5 0x7f06001c
int id accessibility_custom_action_6 0x7f06001d
int id accessibility_custom_action_7 0x7f06001e
int id accessibility_custom_action_8 0x7f06001f
int id accessibility_custom_action_9 0x7f060020
int id action0 0x7f060021
int id action_container 0x7f060022
int id action_divider 0x7f060023
int id action_image 0x7f060024
int id action_text 0x7f060025
int id actions 0x7f060026
int id adjust_height 0x7f060027
int id adjust_width 0x7f060028
int id androidx_window_activity_scope 0x7f060029
int id async 0x7f06002a
int id auto 0x7f06002b
int id blocking 0x7f06002c
int id browser_actions_header_text 0x7f06002d
int id browser_actions_menu_item_icon 0x7f06002e
int id browser_actions_menu_item_text 0x7f06002f
int id browser_actions_menu_items 0x7f060030
int id browser_actions_menu_view 0x7f060031
int id cancel_action 0x7f060032
int id chronometer 0x7f060033
int id dark 0x7f060034
int id dialog_button 0x7f060035
int id end_padder 0x7f060036
int id forever 0x7f060037
int id icon 0x7f060038
int id icon_group 0x7f060039
int id icon_only 0x7f06003a
int id info 0x7f06003b
int id italic 0x7f06003c
int id light 0x7f06003d
int id line1 0x7f06003e
int id line3 0x7f06003f
int id locale 0x7f060040
int id ltr 0x7f060041
int id media_actions 0x7f060042
int id none 0x7f060043
int id normal 0x7f060044
int id notification_background 0x7f060045
int id notification_main_column 0x7f060046
int id notification_main_column_container 0x7f060047
int id right_icon 0x7f060048
int id right_side 0x7f060049
int id rtl 0x7f06004a
int id standard 0x7f06004b
int id status_bar_latest_event_content 0x7f06004c
int id tag_accessibility_actions 0x7f06004d
int id tag_accessibility_clickable_spans 0x7f06004e
int id tag_accessibility_heading 0x7f06004f
int id tag_accessibility_pane_title 0x7f060050
int id tag_on_apply_window_listener 0x7f060051
int id tag_on_receive_content_listener 0x7f060052
int id tag_on_receive_content_mime_types 0x7f060053
int id tag_screen_reader_focusable 0x7f060054
int id tag_state_description 0x7f060055
int id tag_transition_group 0x7f060056
int id tag_unhandled_key_event_manager 0x7f060057
int id tag_unhandled_key_listeners 0x7f060058
int id tag_window_insets_animation_callback 0x7f060059
int id text 0x7f06005a
int id text2 0x7f06005b
int id time 0x7f06005c
int id title 0x7f06005d
int id view_tree_lifecycle_owner 0x7f06005e
int id wide 0x7f06005f
int integer cancel_button_image_alpha 0x7f070000
int integer google_play_services_version 0x7f070001
int integer status_bar_notification_info_maxnum 0x7f070002
int layout browser_actions_context_menu_page 0x7f080000
int layout browser_actions_context_menu_row 0x7f080001
int layout custom_dialog 0x7f080002
int layout notification_action 0x7f080003
int layout notification_action_tombstone 0x7f080004
int layout notification_media_action 0x7f080005
int layout notification_media_cancel_action 0x7f080006
int layout notification_template_big_media 0x7f080007
int layout notification_template_big_media_custom 0x7f080008
int layout notification_template_big_media_narrow 0x7f080009
int layout notification_template_big_media_narrow_custom 0x7f08000a
int layout notification_template_custom_big 0x7f08000b
int layout notification_template_icon_group 0x7f08000c
int layout notification_template_lines_media 0x7f08000d
int layout notification_template_media 0x7f08000e
int layout notification_template_media_custom 0x7f08000f
int layout notification_template_part_chronometer 0x7f080010
int layout notification_template_part_time 0x7f080011
int mipmap ic_launcher 0x7f090000
int raw firebase_common_keep 0x7f0a0000
int string androidx_startup 0x7f0b0000
int string call_notification_answer_action 0x7f0b0001
int string call_notification_answer_video_action 0x7f0b0002
int string call_notification_decline_action 0x7f0b0003
int string call_notification_hang_up_action 0x7f0b0004
int string call_notification_incoming_text 0x7f0b0005
int string call_notification_ongoing_text 0x7f0b0006
int string call_notification_screening_text 0x7f0b0007
int string common_google_play_services_enable_button 0x7f0b0008
int string common_google_play_services_enable_text 0x7f0b0009
int string common_google_play_services_enable_title 0x7f0b000a
int string common_google_play_services_install_button 0x7f0b000b
int string common_google_play_services_install_text 0x7f0b000c
int string common_google_play_services_install_title 0x7f0b000d
int string common_google_play_services_notification_channel_name 0x7f0b000e
int string common_google_play_services_notification_ticker 0x7f0b000f
int string common_google_play_services_unknown_issue 0x7f0b0010
int string common_google_play_services_unsupported_text 0x7f0b0011
int string common_google_play_services_update_button 0x7f0b0012
int string common_google_play_services_update_text 0x7f0b0013
int string common_google_play_services_update_title 0x7f0b0014
int string common_google_play_services_updating_text 0x7f0b0015
int string common_google_play_services_wear_update_text 0x7f0b0016
int string common_open_on_phone 0x7f0b0017
int string common_signin_button_text 0x7f0b0018
int string common_signin_button_text_long 0x7f0b0019
int string copy_toast_msg 0x7f0b001a
int string fallback_menu_item_copy_link 0x7f0b001b
int string fallback_menu_item_open_in_browser 0x7f0b001c
int string fallback_menu_item_share_link 0x7f0b001d
int string fcm_fallback_notification_channel_label 0x7f0b001e
int string gcm_defaultSenderId 0x7f0b001f
int string google_api_key 0x7f0b0020
int string google_app_id 0x7f0b0021
int string google_crash_reporting_api_key 0x7f0b0022
int string google_storage_bucket 0x7f0b0023
int string project_id 0x7f0b0024
int string status_bar_notification_info_overflow 0x7f0b0025
int style LaunchTheme 0x7f0c0000
int style NormalTheme 0x7f0c0001
int style TextAppearance_Compat_Notification 0x7f0c0002
int style TextAppearance_Compat_Notification_Info 0x7f0c0003
int style TextAppearance_Compat_Notification_Info_Media 0x7f0c0004
int style TextAppearance_Compat_Notification_Line2 0x7f0c0005
int style TextAppearance_Compat_Notification_Line2_Media 0x7f0c0006
int style TextAppearance_Compat_Notification_Media 0x7f0c0007
int style TextAppearance_Compat_Notification_Time 0x7f0c0008
int style TextAppearance_Compat_Notification_Time_Media 0x7f0c0009
int style TextAppearance_Compat_Notification_Title 0x7f0c000a
int style TextAppearance_Compat_Notification_Title_Media 0x7f0c000b
int style Widget_Compat_NotificationActionContainer 0x7f0c000c
int style Widget_Compat_NotificationActionText 0x7f0c000d
int[] styleable ActivityFilter { 0x7f010000, 0x7f010001 }
int styleable ActivityFilter_activityAction 0
int styleable ActivityFilter_activityName 1
int[] styleable ActivityRule { 0x7f010003 }
int styleable ActivityRule_alwaysExpand 0
int[] styleable Capability { 0x7f01001b, 0x7f01001f }
int styleable Capability_queryPatterns 0
int styleable Capability_shortcutMatchRequired 1
int[] styleable ColorStateListItem { 0x010101a5, 0x0101031f, 0x01010647, 0x7f010002, 0x7f010017 }
int styleable ColorStateListItem_android_color 0
int styleable ColorStateListItem_android_alpha 1
int styleable ColorStateListItem_android_lStar 2
int styleable ColorStateListItem_alpha 3
int styleable ColorStateListItem_lStar 4
int[] styleable FontFamily { 0x7f01000b, 0x7f01000c, 0x7f01000d, 0x7f01000e, 0x7f01000f, 0x7f010010, 0x7f010011 }
int styleable FontFamily_fontProviderAuthority 0
int styleable FontFamily_fontProviderCerts 1
int styleable FontFamily_fontProviderFetchStrategy 2
int styleable FontFamily_fontProviderFetchTimeout 3
int styleable FontFamily_fontProviderPackage 4
int styleable FontFamily_fontProviderQuery 5
int styleable FontFamily_fontProviderSystemFontFamily 6
int[] styleable FontFamilyFont { 0x01010532, 0x01010533, 0x0101053f, 0x0101056f, 0x01010570, 0x7f01000a, 0x7f010012, 0x7f010013, 0x7f010014, 0x7f010024 }
int styleable FontFamilyFont_android_font 0
int styleable FontFamilyFont_android_fontWeight 1
int styleable FontFamilyFont_android_fontStyle 2
int styleable FontFamilyFont_android_ttcIndex 3
int styleable FontFamilyFont_android_fontVariationSettings 4
int styleable FontFamilyFont_font 5
int styleable FontFamilyFont_fontStyle 6
int styleable FontFamilyFont_fontVariationSettings 7
int styleable FontFamilyFont_fontWeight 8
int styleable FontFamilyFont_ttcIndex 9
int[] styleable GradientColor { 0x0101019d, 0x0101019e, 0x010101a1, 0x010101a2, 0x010101a3, 0x010101a4, 0x01010201, 0x0101020b, 0x01010510, 0x01010511, 0x01010512, 0x01010513 }
int styleable GradientColor_android_startColor 0
int styleable GradientColor_android_endColor 1
int styleable GradientColor_android_type 2
int styleable GradientColor_android_centerX 3
int styleable GradientColor_android_centerY 4
int styleable GradientColor_android_gradientRadius 5
int styleable GradientColor_android_tileMode 6
int styleable GradientColor_android_centerColor 7
int styleable GradientColor_android_startX 8
int styleable GradientColor_android_startY 9
int styleable GradientColor_android_endX 10
int styleable GradientColor_android_endY 11
int[] styleable GradientColorItem { 0x010101a5, 0x01010514 }
int styleable GradientColorItem_android_color 0
int styleable GradientColorItem_android_offset 1
int[] styleable LoadingImageView { 0x7f010005, 0x7f010015, 0x7f010016 }
int styleable LoadingImageView_circleCrop 0
int styleable LoadingImageView_imageAspectRatio 1
int styleable LoadingImageView_imageAspectRatioAdjust 2
int[] styleable SignInButton { 0x7f010004, 0x7f010007, 0x7f01001c }
int styleable SignInButton_buttonSize 0
int styleable SignInButton_colorScheme 1
int styleable SignInButton_scopeUris 2
int[] styleable SplitPairFilter { 0x7f01001a, 0x7f01001d, 0x7f01001e }
int styleable SplitPairFilter_primaryActivityName 0
int styleable SplitPairFilter_secondaryActivityAction 1
int styleable SplitPairFilter_secondaryActivityName 2
int[] styleable SplitPairRule { 0x7f010006, 0x7f010008, 0x7f010009, 0x7f010020, 0x7f010021, 0x7f010022, 0x7f010023 }
int styleable SplitPairRule_clearTop 0
int styleable SplitPairRule_finishPrimaryWithSecondary 1
int styleable SplitPairRule_finishSecondaryWithPrimary 2
int styleable SplitPairRule_splitLayoutDirection 3
int styleable SplitPairRule_splitMinSmallestWidth 4
int styleable SplitPairRule_splitMinWidth 5
int styleable SplitPairRule_splitRatio 6
int[] styleable SplitPlaceholderRule { 0x7f010019, 0x7f010020, 0x7f010021, 0x7f010022, 0x7f010023 }
int styleable SplitPlaceholderRule_placeholderActivityName 0
int styleable SplitPlaceholderRule_splitLayoutDirection 1
int styleable SplitPlaceholderRule_splitMinSmallestWidth 2
int styleable SplitPlaceholderRule_splitMinWidth 3
int styleable SplitPlaceholderRule_splitRatio 4
int xml image_share_filepaths 0x7f0e0000
