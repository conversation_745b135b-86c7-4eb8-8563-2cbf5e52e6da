1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.lekky.app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
8-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml
9        android:targetSdkVersion="34" />
9-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml
10    <!-- Add permissions for notifications -->
11    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
11-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:4:5-76
11-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:4:22-74
12    <!-- Add permission for exact alarms (Android 12+ / API 31+) -->
13    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
13-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:6:5-78
13-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:6:22-76
14    <!-- Add permission for boot completed to reschedule notifications -->
15    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
15-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:8:5-80
15-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:8:22-78
16    <!-- Add permissions for background work -->
17    <uses-permission android:name="android.permission.WAKE_LOCK" />
17-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:10:5-67
17-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:10:22-65
18    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
18-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:11:5-76
18-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:11:22-74
19    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
19-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:12:5-86
19-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:12:22-84
20    <!-- Add permission to ignore battery optimizations -->
21    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
21-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:14:5-94
21-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:14:22-92
22    <!-- Add permission for alarm clock notifications -->
23    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />
23-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:16:5-73
23-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:16:22-71
24
25    <!-- Storage permissions for file export -->
26    <uses-permission
26-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:19:5-21:38
27        android:name="android.permission.READ_EXTERNAL_STORAGE"
27-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:20:9-64
28        android:maxSdkVersion="32" />
28-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:21:9-35
29    <uses-permission
29-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:22:5-24:38
30        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
30-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:23:9-65
31        android:maxSdkVersion="29" />
31-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:24:9-35
32
33    <!-- Android 13+ (API 33+) granular media permissions -->
34    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
34-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:27:5-76
34-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:27:22-73
35    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
35-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:28:5-75
35-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:28:22-72
36    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
36-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:29:5-75
36-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:29:22-72
37
38    <!-- Android 14+ (API 34+) partial media access -->
39    <uses-permission android:name="android.permission.READ_MEDIA_VISUAL_USER_SELECTED" />
39-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:32:5-90
39-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:32:22-87
40
41    <!--
42         Required to query activities that can process text, see:
43         https://developer.android.com/training/package-visibility?hl=en and
44         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
45
46         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
47    -->
48    <queries>
48-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:99:5-116:15
49        <intent>
49-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:100:9-103:18
50            <action android:name="android.intent.action.PROCESS_TEXT" />
50-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:101:13-72
50-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:101:21-70
51
52            <data android:mimeType="text/plain" />
52-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:102:13-50
52-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:102:19-48
53        </intent>
54        <!-- For document selection -->
55        <intent>
55-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:105:9-107:18
56            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
56-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:106:13-79
56-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:106:21-76
57        </intent>
58        <intent>
58-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:108:9-111:18
59            <action android:name="android.intent.action.CREATE_DOCUMENT" />
59-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:109:13-76
59-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:109:21-73
60
61            <data android:mimeType="text/csv" />
61-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:102:13-50
61-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:102:19-48
62        </intent>
63        <intent>
63-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:112:9-115:18
64            <action android:name="android.intent.action.OPEN_DOCUMENT" />
64-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:113:13-74
64-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:113:21-71
65
66            <data android:mimeType="text/csv" />
66-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:102:13-50
66-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:102:19-48
67        </intent>
68        <intent>
68-->[:file_picker] D:\000.Workspace\Lekky\build\file_picker\intermediates\merged_manifest\release\AndroidManifest.xml:12:9-16:18
69            <action android:name="android.intent.action.GET_CONTENT" />
69-->[:file_picker] D:\000.Workspace\Lekky\build\file_picker\intermediates\merged_manifest\release\AndroidManifest.xml:13:13-72
69-->[:file_picker] D:\000.Workspace\Lekky\build\file_picker\intermediates\merged_manifest\release\AndroidManifest.xml:13:21-69
70
71            <data android:mimeType="*/*" />
71-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:102:13-50
71-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:102:19-48
72        </intent>
73    </queries>
74
75    <uses-permission android:name="android.permission.INTERNET" />
75-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\release\AndroidManifest.xml:9:5-67
75-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\release\AndroidManifest.xml:9:22-64
76    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
76-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\release\AndroidManifest.xml:11:5-79
76-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\release\AndroidManifest.xml:11:22-76
77    <uses-permission android:name="android.permission.VIBRATE" /> <!-- Required by older versions of Google Play services to create IID tokens -->
77-->[:flutter_local_notifications] D:\000.Workspace\Lekky\build\flutter_local_notifications\intermediates\merged_manifest\release\AndroidManifest.xml:9:5-66
77-->[:flutter_local_notifications] D:\000.Workspace\Lekky\build\flutter_local_notifications\intermediates\merged_manifest\release\AndroidManifest.xml:9:22-63
78    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
78-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\ef264fe3e2e64afd12a5581102dbb0a0\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:26:5-82
78-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\ef264fe3e2e64afd12a5581102dbb0a0\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:26:22-79
79
80    <permission
80-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
81        android:name="com.lekky.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
81-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
82        android:protectionLevel="signature" />
82-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
83
84    <uses-permission android:name="com.lekky.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
84-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
84-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
85    <!--
86 Note: For Android 10+ (API 29+), we use Storage Access Framework (SAF)
87         which doesn't require additional permissions for user-selected locations
88    -->
89    <application
90        android:name="android.app.Application"
90-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:39:9-42
91        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
91-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
92        android:enableOnBackInvokedCallback="true"
92-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:41:9-51
93        android:icon="@mipmap/ic_launcher"
93-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:40:9-43
94        android:label="lekky"
94-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:38:9-30
95        android:requestLegacyExternalStorage="true" >
95-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:42:9-52
96        <activity
96-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:43:9-63:20
97            android:name="com.lekky.app.MainActivity"
97-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:44:13-41
98            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
98-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:48:13-163
99            android:exported="true"
99-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:45:13-36
100            android:hardwareAccelerated="true"
100-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:49:13-47
101            android:launchMode="singleTop"
101-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:46:13-43
102            android:theme="@style/LaunchTheme"
102-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:47:13-47
103            android:windowSoftInputMode="adjustResize" >
103-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:50:13-55
104
105            <!--
106                 Specifies an Android theme to apply to this Activity as soon as
107                 the Android process has started. This theme is visible to the user
108                 while the Flutter UI initializes. After that, this theme continues
109                 to determine the Window background behind the Flutter UI.
110            -->
111            <meta-data
111-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:55:13-58:19
112                android:name="io.flutter.embedding.android.NormalTheme"
112-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:56:17-72
113                android:resource="@style/NormalTheme" />
113-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:57:17-54
114
115            <intent-filter>
115-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:59:13-62:29
116                <action android:name="android.intent.action.MAIN" />
116-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:60:17-68
116-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:60:25-66
117
118                <category android:name="android.intent.category.LAUNCHER" />
118-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:61:17-76
118-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:61:27-74
119            </intent-filter>
120        </activity>
121        <!--
122             Don't delete the meta-data below.
123             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
124        -->
125        <meta-data
125-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:66:9-68:33
126            android:name="flutterEmbedding"
126-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:67:13-44
127            android:value="2" />
127-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:68:13-30
128
129        <!-- Add receiver for boot completed to reschedule notifications -->
130        <receiver
130-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:71:9-79:20
131            android:name="com.dexterous.flutterlocalnotifications.ScheduledNotificationBootReceiver"
131-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:72:13-101
132            android:exported="true" >
132-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:73:13-36
133            <intent-filter>
133-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:75:13-78:29
134                <action android:name="android.intent.action.BOOT_COMPLETED" />
134-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:76:17-78
134-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:76:25-76
135                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
135-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:77:17-83
135-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:77:25-81
136            </intent-filter>
137        </receiver>
138
139        <!-- Foreground service for reliable notifications -->
140        <service
140-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:82:9-86:56
141            android:name="com.lekky.app.NotificationForegroundService"
141-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:83:13-58
142            android:enabled="true"
142-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:84:13-35
143            android:exported="false"
143-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:85:13-37
144            android:foregroundServiceType="dataSync" />
144-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:86:13-53
145
146        <!-- Package name for the application -->
147        <meta-data
147-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:89:9-91:45
148            android:name="com.lekky.app.PACKAGE_NAME"
148-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:90:13-54
149            android:value="com.lekky.app" />
149-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:91:13-42
150
151        <service
151-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\release\AndroidManifest.xml:16:9-19:72
152            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
152-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\release\AndroidManifest.xml:17:13-107
153            android:exported="false"
153-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\release\AndroidManifest.xml:18:13-37
154            android:permission="android.permission.BIND_JOB_SERVICE" />
154-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\release\AndroidManifest.xml:19:13-69
155        <service
155-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\release\AndroidManifest.xml:20:9-26:19
156            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
156-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\release\AndroidManifest.xml:21:13-97
157            android:exported="false" >
157-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\release\AndroidManifest.xml:22:13-37
158            <intent-filter>
158-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\release\AndroidManifest.xml:23:13-25:29
159                <action android:name="com.google.firebase.MESSAGING_EVENT" />
159-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\release\AndroidManifest.xml:24:17-78
159-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\release\AndroidManifest.xml:24:25-75
160            </intent-filter>
161        </service>
162
163        <receiver
163-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\release\AndroidManifest.xml:28:9-35:20
164            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
164-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\release\AndroidManifest.xml:29:13-98
165            android:exported="true"
165-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\release\AndroidManifest.xml:30:13-36
166            android:permission="com.google.android.c2dm.permission.SEND" >
166-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\release\AndroidManifest.xml:31:13-73
167            <intent-filter>
167-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\release\AndroidManifest.xml:32:13-34:29
168                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
168-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\release\AndroidManifest.xml:33:17-81
168-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\release\AndroidManifest.xml:33:25-78
169            </intent-filter>
170        </receiver>
171
172        <service
172-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\release\AndroidManifest.xml:37:9-41:19
173            android:name="com.google.firebase.components.ComponentDiscoveryService"
173-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\release\AndroidManifest.xml:37:18-89
174            android:directBootAware="true"
174-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\f96cbbc8275bf5c236dff6879a5d69c0\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:32:13-43
175            android:exported="false" >
175-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\ef264fe3e2e64afd12a5581102dbb0a0\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:56:13-37
176            <meta-data
176-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\release\AndroidManifest.xml:38:13-40:85
177                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
177-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\release\AndroidManifest.xml:39:17-128
178                android:value="com.google.firebase.components.ComponentRegistrar" />
178-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\release\AndroidManifest.xml:40:17-82
179            <meta-data
179-->[:firebase_core] D:\000.Workspace\Lekky\build\firebase_core\intermediates\merged_manifest\release\AndroidManifest.xml:11:13-13:85
180                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
180-->[:firebase_core] D:\000.Workspace\Lekky\build\firebase_core\intermediates\merged_manifest\release\AndroidManifest.xml:12:17-124
181                android:value="com.google.firebase.components.ComponentRegistrar" />
181-->[:firebase_core] D:\000.Workspace\Lekky\build\firebase_core\intermediates\merged_manifest\release\AndroidManifest.xml:13:17-82
182            <meta-data
182-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\ef264fe3e2e64afd12a5581102dbb0a0\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:57:13-59:85
183                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
183-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\ef264fe3e2e64afd12a5581102dbb0a0\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:58:17-122
184                android:value="com.google.firebase.components.ComponentRegistrar" />
184-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\ef264fe3e2e64afd12a5581102dbb0a0\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:59:17-82
185            <meta-data
185-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\ef264fe3e2e64afd12a5581102dbb0a0\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:60:13-62:85
186                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
186-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\ef264fe3e2e64afd12a5581102dbb0a0\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:61:17-119
187                android:value="com.google.firebase.components.ComponentRegistrar" />
187-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\ef264fe3e2e64afd12a5581102dbb0a0\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:62:17-82
188            <meta-data
188-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ece8853862d7d2a4aa4a024946a38788\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
189                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
189-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ece8853862d7d2a4aa4a024946a38788\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
190                android:value="com.google.firebase.components.ComponentRegistrar" />
190-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ece8853862d7d2a4aa4a024946a38788\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
191            <meta-data
191-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ece8853862d7d2a4aa4a024946a38788\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
192                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
192-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ece8853862d7d2a4aa4a024946a38788\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
193                android:value="com.google.firebase.components.ComponentRegistrar" />
193-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ece8853862d7d2a4aa4a024946a38788\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
194            <meta-data
194-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\1bc4a777fc1a043738c99d19a9b712eb\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:12:13-14:85
195                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
195-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\1bc4a777fc1a043738c99d19a9b712eb\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:13:17-116
196                android:value="com.google.firebase.components.ComponentRegistrar" />
196-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\1bc4a777fc1a043738c99d19a9b712eb\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:14:17-82
197            <meta-data
197-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\f96cbbc8275bf5c236dff6879a5d69c0\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:35:13-37:85
198                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
198-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\f96cbbc8275bf5c236dff6879a5d69c0\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:36:17-109
199                android:value="com.google.firebase.components.ComponentRegistrar" />
199-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\f96cbbc8275bf5c236dff6879a5d69c0\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:37:17-82
200            <meta-data
200-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\8596bdabe22499a5bad93b17731f1f21\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:27:13-29:85
201                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
201-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\8596bdabe22499a5bad93b17731f1f21\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:28:17-115
202                android:value="com.google.firebase.components.ComponentRegistrar" />
202-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\8596bdabe22499a5bad93b17731f1f21\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:29:17-82
203        </service>
204
205        <provider
205-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\release\AndroidManifest.xml:43:9-47:38
206            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
206-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\release\AndroidManifest.xml:44:13-102
207            android:authorities="com.lekky.app.flutterfirebasemessaginginitprovider"
207-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\release\AndroidManifest.xml:45:13-88
208            android:exported="false"
208-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\release\AndroidManifest.xml:46:13-37
209            android:initOrder="99" />
209-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\release\AndroidManifest.xml:47:13-35
210
211        <activity
211-->[:url_launcher_android] D:\000.Workspace\Lekky\build\url_launcher_android\intermediates\merged_manifest\release\AndroidManifest.xml:10:9-13:74
212            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
212-->[:url_launcher_android] D:\000.Workspace\Lekky\build\url_launcher_android\intermediates\merged_manifest\release\AndroidManifest.xml:11:13-74
213            android:exported="false"
213-->[:url_launcher_android] D:\000.Workspace\Lekky\build\url_launcher_android\intermediates\merged_manifest\release\AndroidManifest.xml:12:13-37
214            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
214-->[:url_launcher_android] D:\000.Workspace\Lekky\build\url_launcher_android\intermediates\merged_manifest\release\AndroidManifest.xml:13:13-71
215
216        <receiver
216-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\ef264fe3e2e64afd12a5581102dbb0a0\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:29:9-40:20
217            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
217-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\ef264fe3e2e64afd12a5581102dbb0a0\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:30:13-78
218            android:exported="true"
218-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\ef264fe3e2e64afd12a5581102dbb0a0\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:31:13-36
219            android:permission="com.google.android.c2dm.permission.SEND" >
219-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\ef264fe3e2e64afd12a5581102dbb0a0\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:32:13-73
220            <intent-filter>
220-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\release\AndroidManifest.xml:32:13-34:29
221                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
221-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\release\AndroidManifest.xml:33:17-81
221-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\release\AndroidManifest.xml:33:25-78
222            </intent-filter>
223
224            <meta-data
224-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\ef264fe3e2e64afd12a5581102dbb0a0\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:37:13-39:40
225                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
225-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\ef264fe3e2e64afd12a5581102dbb0a0\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:38:17-92
226                android:value="true" />
226-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\ef264fe3e2e64afd12a5581102dbb0a0\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:39:17-37
227        </receiver>
228        <!--
229             FirebaseMessagingService performs security checks at runtime,
230             but set to not exported to explicitly avoid allowing another app to call it.
231        -->
232        <service
232-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\ef264fe3e2e64afd12a5581102dbb0a0\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:46:9-53:19
233            android:name="com.google.firebase.messaging.FirebaseMessagingService"
233-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\ef264fe3e2e64afd12a5581102dbb0a0\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:47:13-82
234            android:directBootAware="true"
234-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\ef264fe3e2e64afd12a5581102dbb0a0\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:48:13-43
235            android:exported="false" >
235-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\ef264fe3e2e64afd12a5581102dbb0a0\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:49:13-37
236            <intent-filter android:priority="-500" >
236-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\release\AndroidManifest.xml:23:13-25:29
237                <action android:name="com.google.firebase.MESSAGING_EVENT" />
237-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\release\AndroidManifest.xml:24:17-78
237-->[:firebase_messaging] D:\000.Workspace\Lekky\build\firebase_messaging\intermediates\merged_manifest\release\AndroidManifest.xml:24:25-75
238            </intent-filter>
239        </service>
240
241        <provider
241-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\f96cbbc8275bf5c236dff6879a5d69c0\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:23:9-28:39
242            android:name="com.google.firebase.provider.FirebaseInitProvider"
242-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\f96cbbc8275bf5c236dff6879a5d69c0\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:24:13-77
243            android:authorities="com.lekky.app.firebaseinitprovider"
243-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\f96cbbc8275bf5c236dff6879a5d69c0\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:25:13-72
244            android:directBootAware="true"
244-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\f96cbbc8275bf5c236dff6879a5d69c0\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:26:13-43
245            android:exported="false"
245-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\f96cbbc8275bf5c236dff6879a5d69c0\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:27:13-37
246            android:initOrder="100" />
246-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\f96cbbc8275bf5c236dff6879a5d69c0\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:28:13-36
247
248        <uses-library
248-->[androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\d8adab4e681d0863a25dfe4be8db95bf\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:25:9-27:40
249            android:name="androidx.window.extensions"
249-->[androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\d8adab4e681d0863a25dfe4be8db95bf\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:26:13-54
250            android:required="false" />
250-->[androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\d8adab4e681d0863a25dfe4be8db95bf\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:27:13-37
251        <uses-library
251-->[androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\d8adab4e681d0863a25dfe4be8db95bf\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:28:9-30:40
252            android:name="androidx.window.sidecar"
252-->[androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\d8adab4e681d0863a25dfe4be8db95bf\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:29:13-51
253            android:required="false" />
253-->[androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\d8adab4e681d0863a25dfe4be8db95bf\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:30:13-37
254
255        <provider
255-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
256            android:name="androidx.startup.InitializationProvider"
256-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:30:13-67
257            android:authorities="com.lekky.app.androidx-startup"
257-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:31:13-68
258            android:exported="false" >
258-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:32:13-37
259            <meta-data
259-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
260                android:name="androidx.work.WorkManagerInitializer"
260-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
261                android:value="androidx.startup" />
261-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
262        </provider>
263
264        <service
264-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
265            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
265-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
266            android:directBootAware="false"
266-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
267            android:enabled="@bool/enable_system_alarm_service_default"
267-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
268            android:exported="false" />
268-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
269        <service
269-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
270            android:name="androidx.work.impl.background.systemjob.SystemJobService"
270-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
271            android:directBootAware="false"
271-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
272            android:enabled="@bool/enable_system_job_service_default"
272-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
273            android:exported="true"
273-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
274            android:permission="android.permission.BIND_JOB_SERVICE" />
274-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
275        <service
275-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
276            android:name="androidx.work.impl.foreground.SystemForegroundService"
276-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
277            android:directBootAware="false"
277-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
278            android:enabled="@bool/enable_system_foreground_service_default"
278-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
279            android:exported="false" />
279-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
280
281        <receiver
281-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
282            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
282-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
283            android:directBootAware="false"
283-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
284            android:enabled="true"
284-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
285            android:exported="false" />
285-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
286        <receiver
286-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
287            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
287-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
288            android:directBootAware="false"
288-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
289            android:enabled="false"
289-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
290            android:exported="false" >
290-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
291            <intent-filter>
291-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
292                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
292-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
292-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
293                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
293-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
293-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
294            </intent-filter>
295        </receiver>
296        <receiver
296-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
297            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
297-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
298            android:directBootAware="false"
298-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
299            android:enabled="false"
299-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
300            android:exported="false" >
300-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
301            <intent-filter>
301-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
302                <action android:name="android.intent.action.BATTERY_OKAY" />
302-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
302-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
303                <action android:name="android.intent.action.BATTERY_LOW" />
303-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
303-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
304            </intent-filter>
305        </receiver>
306        <receiver
306-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
307            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
307-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
308            android:directBootAware="false"
308-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
309            android:enabled="false"
309-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
310            android:exported="false" >
310-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
311            <intent-filter>
311-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
312                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
312-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
312-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
313                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
313-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
313-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
314            </intent-filter>
315        </receiver>
316        <receiver
316-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
317            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
317-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
318            android:directBootAware="false"
318-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
319            android:enabled="false"
319-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
320            android:exported="false" >
320-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
321            <intent-filter>
321-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
322                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
322-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
322-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
323            </intent-filter>
324        </receiver>
325        <receiver
325-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
326            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
326-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
327            android:directBootAware="false"
327-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
328            android:enabled="false"
328-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
329            android:exported="false" >
329-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
330            <intent-filter>
330-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
331                <action android:name="android.intent.action.BOOT_COMPLETED" />
331-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:76:17-78
331-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:76:25-76
332                <action android:name="android.intent.action.TIME_SET" />
332-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
332-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
333                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
333-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
333-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
334            </intent-filter>
335        </receiver>
336        <receiver
336-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
337            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
337-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
338            android:directBootAware="false"
338-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
339            android:enabled="@bool/enable_system_alarm_service_default"
339-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
340            android:exported="false" >
340-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
341            <intent-filter>
341-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
342                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
342-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
342-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
343            </intent-filter>
344        </receiver>
345        <receiver
345-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
346            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
346-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
347            android:directBootAware="false"
347-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
348            android:enabled="true"
348-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
349            android:exported="true"
349-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
350            android:permission="android.permission.DUMP" >
350-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
351            <intent-filter>
351-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
352                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
352-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
352-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
353            </intent-filter>
354        </receiver>
355
356        <service
356-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\00ee8946583275229f58c64a5ecbb412\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
357            android:name="androidx.room.MultiInstanceInvalidationService"
357-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\00ee8946583275229f58c64a5ecbb412\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
358            android:directBootAware="true"
358-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\00ee8946583275229f58c64a5ecbb412\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
359            android:exported="false" />
359-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\00ee8946583275229f58c64a5ecbb412\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
360
361        <activity
361-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\33a23a1921cc9e261ad760b2275ca606\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
362            android:name="com.google.android.gms.common.api.GoogleApiActivity"
362-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\33a23a1921cc9e261ad760b2275ca606\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:19-85
363            android:exported="false"
363-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\33a23a1921cc9e261ad760b2275ca606\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:22:19-43
364            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
364-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\33a23a1921cc9e261ad760b2275ca606\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:21:19-78
365
366        <meta-data
366-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\473c6549ebdad33ab68cbd325533c84f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
367            android:name="com.google.android.gms.version"
367-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\473c6549ebdad33ab68cbd325533c84f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
368            android:value="@integer/google_play_services_version" />
368-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\473c6549ebdad33ab68cbd325533c84f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
369
370        <service
370-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\02d4b24fbb4236fb3b924f02d99ca4fb\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
371            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
371-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\02d4b24fbb4236fb3b924f02d99ca4fb\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
372            android:exported="false" >
372-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\02d4b24fbb4236fb3b924f02d99ca4fb\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
373            <meta-data
373-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\02d4b24fbb4236fb3b924f02d99ca4fb\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
374                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
374-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\02d4b24fbb4236fb3b924f02d99ca4fb\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
375                android:value="cct" />
375-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\02d4b24fbb4236fb3b924f02d99ca4fb\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
376        </service>
377        <service
377-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\5c67e31e1a8fc4f9b0a57abcc0f9d1c6\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
378            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
378-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\5c67e31e1a8fc4f9b0a57abcc0f9d1c6\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
379            android:exported="false"
379-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\5c67e31e1a8fc4f9b0a57abcc0f9d1c6\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
380            android:permission="android.permission.BIND_JOB_SERVICE" >
380-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\5c67e31e1a8fc4f9b0a57abcc0f9d1c6\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
381        </service>
382
383        <receiver
383-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\5c67e31e1a8fc4f9b0a57abcc0f9d1c6\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
384            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
384-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\5c67e31e1a8fc4f9b0a57abcc0f9d1c6\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
385            android:exported="false" />
385-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\5c67e31e1a8fc4f9b0a57abcc0f9d1c6\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
386    </application>
387
388</manifest>
