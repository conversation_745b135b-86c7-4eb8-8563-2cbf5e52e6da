import 'package:flutter/material.dart';
import 'numeric_input_field.dart';

/// A reusable widget for integer input fields that automatically selects all text when focused
class IntegerInputField extends StatelessWidget {
  /// The current value of the field
  final int? value;

  /// Callback when the value changes (called on every keystroke)
  final ValueChanged<int?> onChanged;

  /// Callback when editing is complete (called when user taps done or field loses focus)
  final ValueChanged<int?>? onEditingComplete;

  /// Suffix text to display (e.g., "days")
  final String suffixText;

  /// Label text for the field
  final String labelText;

  /// Hint text for the field
  final String? hintText;

  /// Helper text for the field
  final String? helperText;

  /// Error text for the field
  final String? errorText;

  /// Whether to allow null values
  final bool allowNull;

  /// Minimum allowed value
  final int minValue;

  /// Maximum allowed value
  final int maxValue;

  /// Border radius for the input field
  final BorderRadius? borderRadius;

  /// Constructor
  const IntegerInputField({
    super.key,
    required this.value,
    required this.onChanged,
    required this.suffixText,
    required this.labelText,
    this.hintText,
    this.helperText,
    this.errorText,
    this.allowNull = false,
    this.minValue = 0,
    this.maxValue = 99,
    this.borderRadius,
    this.onEditingComplete,
  });

  @override
  Widget build(BuildContext context) {
    return NumericInputField(
      value: value,
      onChanged: (num? newValue) => onChanged(newValue?.round()),
      onEditingComplete: onEditingComplete != null
          ? (num? newValue) => onEditingComplete!(newValue?.round())
          : null,
      inputType: NumericInputType.integer,
      suffixText: suffixText,
      labelText: labelText,
      hintText: hintText,
      helperText: helperText,
      errorText: errorText,
      allowNull: allowNull,
      minValue: minValue,
      maxValue: maxValue,
      borderRadius: borderRadius,
    );
  }
}
