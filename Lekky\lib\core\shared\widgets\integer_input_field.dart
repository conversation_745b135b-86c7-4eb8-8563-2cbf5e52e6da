import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// A reusable widget for integer input fields that automatically selects all text when focused
class IntegerInputField extends StatefulWidget {
  /// The current value of the field
  final int? value;

  /// Callback when the value changes (called on every keystroke)
  final ValueChanged<int?> onChanged;

  /// Callback when editing is complete (called when user taps done or field loses focus)
  final ValueChanged<int?>? onEditingComplete;

  /// Suffix text to display (e.g., "days")
  final String suffixText;

  /// Label text for the field
  final String labelText;

  /// Hint text for the field
  final String? hintText;

  /// Helper text for the field
  final String? helperText;

  /// Error text for the field
  final String? errorText;

  /// Whether to allow null values
  final bool allowNull;

  /// Minimum allowed value
  final int minValue;

  /// Maximum allowed value
  final int maxValue;

  /// Border radius for the input field
  final BorderRadius? borderRadius;

  /// Constructor
  const IntegerInputField({
    super.key,
    required this.value,
    required this.onChanged,
    required this.suffixText,
    required this.labelText,
    this.hintText,
    this.helperText,
    this.errorText,
    this.allowNull = false,
    this.minValue = 0,
    this.maxValue = 99,
    this.borderRadius,
    this.onEditingComplete,
  });

  @override
  State<IntegerInputField> createState() => _IntegerInputFieldState();
}

class _IntegerInputFieldState extends State<IntegerInputField> {
  late TextEditingController _controller;
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(
      text: widget.value?.toString() ?? (widget.allowNull ? '' : '0'),
    );
    _focusNode = FocusNode()
      ..addListener(() {
        if (_focusNode.hasFocus) {
          _controller.selection = TextSelection(
            baseOffset: 0,
            extentOffset: _controller.text.length,
          );
        }
      });
  }

  @override
  void didUpdateWidget(covariant IntegerInputField oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Only update the controller text if the external value changed and we're not focused
    if (!_focusNode.hasFocus &&
        ((widget.value != oldWidget.value) ||
            (widget.value == null && oldWidget.value != null) ||
            (widget.value != null && oldWidget.value == null))) {
      _controller.text =
          widget.value?.toString() ?? (widget.allowNull ? '' : '0');
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final effectiveBorderRadius =
        widget.borderRadius ?? BorderRadius.circular(8);

    return TextField(
      controller: _controller,
      focusNode: _focusNode,
      keyboardType: TextInputType.number,
      inputFormatters: [
        FilteringTextInputFormatter.digitsOnly,
      ],
      decoration: InputDecoration(
        labelText: widget.labelText,
        hintText: widget.hintText,
        suffixText: widget.suffixText,
        border: OutlineInputBorder(
          borderRadius: effectiveBorderRadius,
        ),
        errorText: widget.errorText,
        helperText: widget.helperText ??
            'Must be between ${widget.minValue} and ${widget.maxValue} ${widget.suffixText}',
        contentPadding:
            const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
      onChanged: (value) {
        if (value.isEmpty) {
          if (widget.allowNull) {
            widget.onChanged(null);
          } else {
            widget.onChanged(0);
          }
        } else {
          final parsedValue = int.tryParse(value);
          if (parsedValue != null &&
              parsedValue >= widget.minValue &&
              parsedValue <= widget.maxValue) {
            widget.onChanged(parsedValue);
          }
        }
      },
      onEditingComplete: () {
        if (widget.onEditingComplete != null) {
          final value = _controller.text;
          if (value.isEmpty) {
            if (widget.allowNull) {
              widget.onEditingComplete!(null);
            } else {
              widget.onEditingComplete!(0);
            }
          } else {
            final parsedValue = int.tryParse(value);
            if (parsedValue != null &&
                parsedValue >= widget.minValue &&
                parsedValue <= widget.maxValue) {
              widget.onEditingComplete!(parsedValue);
            }
          }
        }
      },
      onTapOutside: (event) {
        _focusNode.unfocus();
        if (widget.onEditingComplete != null) {
          final value = _controller.text;
          if (value.isEmpty) {
            if (widget.allowNull) {
              widget.onEditingComplete!(null);
            } else {
              widget.onEditingComplete!(0);
            }
          } else {
            final parsedValue = int.tryParse(value);
            if (parsedValue != null &&
                parsedValue >= widget.minValue &&
                parsedValue <= widget.maxValue) {
              widget.onEditingComplete!(parsedValue);
            }
          }
        }
      },
      showCursor: true,
      autofocus: false,
    );
  }
}
