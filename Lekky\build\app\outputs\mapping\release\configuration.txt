# The proguard configuration file for the following section is D:\000.Workspace\Lekky\build\app\intermediates\default_proguard_files\global\proguard-android.txt-7.3.0
# This is a configuration file for ProGuard.
# http://proguard.sourceforge.net/index.html#manual/usage.html
#
# Starting with version 2.2 of the Android plugin for Gradle, this file is distributed together with
# the plugin and unpacked at build-time. The files in $ANDROID_HOME are no longer maintained and
# will be ignored by new version of the Android plugin for Gradle.

# Optimization is turned off by default. Dex does not like code run
# through the ProGuard optimize steps (and performs some
# of these optimizations on its own).
# Note that if you want to enable optimization, you cannot just
# include optimization flags in your own project configuration file;
# instead you will need to point to the
# "proguard-android-optimize.txt" file instead of this one from your
# project.properties file.
-dontoptimize

-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-verbose

# Preserve some attributes that may be required for reflection.
-keepattributes AnnotationDefault,
                EnclosingMethod,
                InnerClasses,
                RuntimeVisibleAnnotations,
                RuntimeVisibleParameterAnnotations,
                RuntimeVisibleTypeAnnotations,
                Signature

-keep public class com.google.vending.licensing.ILicensingService
-keep public class com.android.vending.licensing.ILicensingService
-keep public class com.google.android.vending.licensing.ILicensingService
-dontnote com.android.vending.licensing.ILicensingService
-dontnote com.google.vending.licensing.ILicensingService
-dontnote com.google.android.vending.licensing.ILicensingService

# For native methods, see http://proguard.sourceforge.net/manual/examples.html#native
-keepclasseswithmembernames,includedescriptorclasses class * {
    native <methods>;
}

# Keep setters in Views so that animations can still work.
-keepclassmembers public class * extends android.view.View {
    void set*(***);
    *** get*();
}

# We want to keep methods in Activity that could be used in the XML attribute onClick.
-keepclassmembers class * extends android.app.Activity {
    public void *(android.view.View);
}

# For enumeration classes, see http://proguard.sourceforge.net/manual/examples.html#enumerations
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

-keepclassmembers class * implements android.os.Parcelable {
    public static final ** CREATOR;
}

# Preserve annotated Javascript interface methods.
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# The support libraries contains references to newer platform versions.
# Don't warn about those in case this app is linking against an older
# platform version. We know about them, and they are safe.
-dontnote android.support.**
-dontnote androidx.**
-dontwarn android.support.**
-dontwarn androidx.**

# This class is deprecated, but remains for backward compatibility.
-dontwarn android.util.FloatMath

# Understand the @Keep support annotation.
-keep class android.support.annotation.Keep
-keep class androidx.annotation.Keep

-keep @android.support.annotation.Keep class * {*;}
-keep @androidx.annotation.Keep class * {*;}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <methods>;
}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <methods>;
}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <fields>;
}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <fields>;
}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <init>(...);
}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <init>(...);
}

# These classes are duplicated between android.jar and org.apache.http.legacy.jar.
-dontnote org.apache.http.**
-dontnote android.net.http.**

# These classes are duplicated between android.jar and core-lambda-stubs.jar.
-dontnote java.lang.invoke.**

# End of content from D:\000.Workspace\Lekky\build\app\intermediates\default_proguard_files\global\proguard-android.txt-7.3.0
# The proguard configuration file for the following section is C:\src\flutter\packages\flutter_tools\gradle\flutter_proguard_rules.pro
# Build the ephemeral app in a module project.
# Prevents: Warning: library class <plugin-package> depends on program class io.flutter.plugin.**
# This is due to plugins (libraries) depending on the embedding (the program jar)
-dontwarn io.flutter.plugin.**

# The android.** package is provided by the OS at runtime.
-dontwarn android.**

# End of content from C:\src\flutter\packages\flutter_tools\gradle\flutter_proguard_rules.pro
# The proguard configuration file for the following section is D:\000.Workspace\Lekky\android\app\proguard-rules.pro
# Keep Flutter classes
-keep class io.flutter.** { *; }
-keep class androidx.** { *; }

# Keep classes used by flutter_local_notifications
-keep class com.dexterous.flutterlocalnotifications.** { *; }

# Keep classes used by sqflite
-keep class com.tekartik.sqflite.** { *; }

# Keep classes used by path_provider
-keep class io.flutter.plugins.pathprovider.** { *; }

# Keep classes used by shared_preferences
-keep class io.flutter.plugins.sharedpreferences.** { *; }
# End of content from D:\000.Workspace\Lekky\android\app\proguard-rules.pro
# The proguard configuration file for the following section is D:\000.Workspace\Lekky\build\app\intermediates\default_proguard_files\global\proguard-android-optimize.txt-7.3.0
# This is a configuration file for ProGuard.
# http://proguard.sourceforge.net/index.html#manual/usage.html
#
# Starting with version 2.2 of the Android plugin for Gradle, this file is distributed together with
# the plugin and unpacked at build-time. The files in $ANDROID_HOME are no longer maintained and
# will be ignored by new version of the Android plugin for Gradle.

# Optimizations: If you don't want to optimize, use the proguard-android.txt configuration file
# instead of this one, which turns off the optimization flags.
# Adding optimization introduces certain risks, since for example not all optimizations performed by
# ProGuard works on all versions of Dalvik.  The following flags turn off various optimizations
# known to have issues, but the list may not be complete or up to date. (The "arithmetic"
# optimization can be used if you are only targeting Android 2.0 or later.)  Make sure you test
# thoroughly if you go this route.
-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*
-optimizationpasses 5
-allowaccessmodification

-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-verbose

# Preserve some attributes that may be required for reflection.
-keepattributes AnnotationDefault,
                EnclosingMethod,
                InnerClasses,
                RuntimeVisibleAnnotations,
                RuntimeVisibleParameterAnnotations,
                RuntimeVisibleTypeAnnotations,
                Signature

-keep public class com.google.vending.licensing.ILicensingService
-keep public class com.android.vending.licensing.ILicensingService
-keep public class com.google.android.vending.licensing.ILicensingService
-dontnote com.android.vending.licensing.ILicensingService
-dontnote com.google.vending.licensing.ILicensingService
-dontnote com.google.android.vending.licensing.ILicensingService

# For native methods, see http://proguard.sourceforge.net/manual/examples.html#native
-keepclasseswithmembernames,includedescriptorclasses class * {
    native <methods>;
}

# Keep setters in Views so that animations can still work.
-keepclassmembers public class * extends android.view.View {
    void set*(***);
    *** get*();
}

# We want to keep methods in Activity that could be used in the XML attribute onClick.
-keepclassmembers class * extends android.app.Activity {
    public void *(android.view.View);
}

# For enumeration classes, see http://proguard.sourceforge.net/manual/examples.html#enumerations
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

-keepclassmembers class * implements android.os.Parcelable {
    public static final ** CREATOR;
}

# Preserve annotated Javascript interface methods.
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# The support libraries contains references to newer platform versions.
# Don't warn about those in case this app is linking against an older
# platform version. We know about them, and they are safe.
-dontnote android.support.**
-dontnote androidx.**
-dontwarn android.support.**
-dontwarn androidx.**

# This class is deprecated, but remains for backward compatibility.
-dontwarn android.util.FloatMath

# Understand the @Keep support annotation.
-keep class android.support.annotation.Keep
-keep class androidx.annotation.Keep

-keep @android.support.annotation.Keep class * {*;}
-keep @androidx.annotation.Keep class * {*;}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <methods>;
}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <methods>;
}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <fields>;
}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <fields>;
}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <init>(...);
}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <init>(...);
}

# These classes are duplicated between android.jar and org.apache.http.legacy.jar.
-dontnote org.apache.http.**
-dontnote android.net.http.**

# These classes are duplicated between android.jar and core-lambda-stubs.jar.
-dontnote java.lang.invoke.**

# End of content from D:\000.Workspace\Lekky\build\app\intermediates\default_proguard_files\global\proguard-android-optimize.txt-7.3.0
# The proguard configuration file for the following section is D:\000.Workspace\Lekky\build\app\intermediates\aapt_proguard_file\release\aapt_rules.txt
-keep class android.app.Application { <init>(); }
-keep class androidx.core.app.CoreComponentFactory { <init>(); }
-keep class androidx.room.MultiInstanceInvalidationService { <init>(); }
-keep class androidx.startup.InitializationProvider { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver { <init>(); }
-keep class androidx.work.impl.background.systemalarm.RescheduleReceiver { <init>(); }
-keep class androidx.work.impl.background.systemalarm.SystemAlarmService { <init>(); }
-keep class androidx.work.impl.background.systemjob.SystemJobService { <init>(); }
-keep class androidx.work.impl.diagnostics.DiagnosticsReceiver { <init>(); }
-keep class androidx.work.impl.foreground.SystemForegroundService { <init>(); }
-keep class androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver { <init>(); }
-keep class com.dexterous.flutterlocalnotifications.ScheduledNotificationBootReceiver { <init>(); }
-keep class com.google.android.datatransport.runtime.backends.TransportBackendDiscovery { <init>(); }
-keep class com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver { <init>(); }
-keep class com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService { <init>(); }
-keep class com.google.android.gms.common.api.GoogleApiActivity { <init>(); }
-keep class com.google.firebase.components.ComponentDiscoveryService { <init>(); }
-keep class com.google.firebase.iid.FirebaseInstanceIdReceiver { <init>(); }
-keep class com.google.firebase.messaging.FirebaseMessagingService { <init>(); }
-keep class com.google.firebase.provider.FirebaseInitProvider { <init>(); }
-keep class com.lekky.app.MainActivity { <init>(); }
-keep class com.lekky.app.NotificationForegroundService { <init>(); }
-keep class io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService { <init>(); }
-keep class io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider { <init>(); }
-keep class io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver { <init>(); }
-keep class io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService { <init>(); }
-keep class io.flutter.plugins.urllauncher.WebViewActivity { <init>(); }
-keep class androidx.browser.browseractions.BrowserActionsFallbackMenuView { <init>(android.content.Context, android.util.AttributeSet); }


# End of content from D:\000.Workspace\Lekky\build\app\intermediates\aapt_proguard_file\release\aapt_rules.txt
# The proguard configuration file for the following section is D:\000.Workspace\Lekky\build\flutter_plugin_android_lifecycle\intermediates\consumer_proguard_dir\release\lib0\proguard.txt
# The point of this package is to specify that a dependent plugin intends to
# use the AndroidX lifecycle classes. Make sure no R8 heuristics shrink classes
# brought in by the embedding's pom.
#
# This isn't strictly needed since by definition, plugins using Android
# lifecycles should implement DefaultLifecycleObserver and therefore keep it
# from being shrunk. But there seems to be an R8 bug so this needs to stay
# https://issuetracker.google.com/issues/142778206.
-keep class androidx.lifecycle.DefaultLifecycleObserver

# End of content from D:\000.Workspace\Lekky\build\flutter_plugin_android_lifecycle\intermediates\consumer_proguard_dir\release\lib0\proguard.txt
# The proguard configuration file for the following section is D:\000.Workspace\Lekky\build\integration_test\intermediates\consumer_proguard_dir\release\lib0\proguard.txt
# For some reason org.kxml2.io.KXmlParser and org.kxml2.io.KXmlSerializer
# are missing and not marked correctly by dependencies.
# Possibly related to https://github.com/flutter/flutter/issues/56591
# See https://github.com/flutter/flutter/issues/127388 for more context.
-dontwarn org.kxml2.io.KXmlParser**,org.kxml2.io.KXmlSerializer**
# End of content from D:\000.Workspace\Lekky\build\integration_test\intermediates\consumer_proguard_dir\release\lib0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\f96cbbc8275bf5c236dff6879a5d69c0\transformed\jetified-firebase-common-20.4.3\proguard.txt
-dontwarn com.google.firebase.platforminfo.KotlinDetector
-dontwarn com.google.auto.value.AutoValue
-dontwarn com.google.auto.value.AutoValue$Builder

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\f96cbbc8275bf5c236dff6879a5d69c0\transformed\jetified-firebase-common-20.4.3\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\d8adab4e681d0863a25dfe4be8db95bf\transformed\jetified-window-1.0.0-beta04\proguard.txt
# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Some methods in androidx.window.extensions are accessed through reflection and need to be kept.
# Failure to do so can cause bugs such as b/157286362. This could be overly broad too and should
# ideally be trimmed down to only the classes/methods that actually need to be kept. This should
# be tracked in b/165268619.
-keep class androidx.window.extensions.** { *; }
-dontwarn androidx.window.extensions.**
-keep class androidx.window.extensions.embedding.** { *; }
-dontwarn androidx.window.extensions.embedding.**

# Keep the whole library for now since there is a crash with a missing method.
# TODO(b/165268619) Make a narrow rule
-keep class androidx.window.** { *; }

# We also neep to keep sidecar.** for the same reason.
-keep class androidx.window.sidecar.** { *; }
-dontwarn androidx.window.sidecar.**


# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\d8adab4e681d0863a25dfe4be8db95bf\transformed\jetified-window-1.0.0-beta04\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\a15b3e96c8a5bbc0219b8abc45b819c0\transformed\rules\lib\META-INF\com.android.tools\r8-from-1.6.0\coroutines.pro
# Allow R8 to optimize away the FastServiceLoader.
# Together with ServiceLoader optimization in R8
# this results in direct instantiation when loading Dispatchers.Main
-assumenosideeffects class kotlinx.coroutines.internal.MainDispatcherLoader {
    boolean FAST_SERVICE_LOADER_ENABLED return false;
}

-assumenosideeffects class kotlinx.coroutines.internal.FastServiceLoaderKt {
    boolean ANDROID_DETECTED return true;
}

# Disable support for "Missing Main Dispatcher", since we always have Android main dispatcher
-assumenosideeffects class kotlinx.coroutines.internal.MainDispatchersKt {
    boolean SUPPORT_MISSING return false;
}

# Statically turn off all debugging facilities and assertions
-assumenosideeffects class kotlinx.coroutines.DebugKt {
    boolean getASSERTIONS_ENABLED() return false;
    boolean getDEBUG() return false;
    boolean getRECOVER_STACK_TRACES() return false;
}

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\a15b3e96c8a5bbc0219b8abc45b819c0\transformed\rules\lib\META-INF\com.android.tools\r8-from-1.6.0\coroutines.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\606b4d93e4b147e72841e5146c5d0fa3\transformed\rules\lib\META-INF\com.android.tools\r8\coroutines.pro
# When editing this file, update the following files as well:
# - META-INF/proguard/coroutines.pro
# - META-INF/com.android.tools/proguard/coroutines.pro

# Most of volatile fields are updated with AFU and should not be mangled
-keepclassmembers class kotlinx.coroutines.** {
    volatile <fields>;
}

# Same story for the standard library's SafeContinuation that also uses AtomicReferenceFieldUpdater
-keepclassmembers class kotlin.coroutines.SafeContinuation {
    volatile <fields>;
}

# These classes are only required by kotlinx.coroutines.debug.AgentPremain, which is only loaded when
# kotlinx-coroutines-core is used as a Java agent, so these are not needed in contexts where ProGuard is used.
-dontwarn java.lang.instrument.ClassFileTransformer
-dontwarn sun.misc.SignalHandler
-dontwarn java.lang.instrument.Instrumentation
-dontwarn sun.misc.Signal

# Only used in `kotlinx.coroutines.internal.ExceptionsConstructor`.
# The case when it is not available is hidden in a `try`-`catch`, as well as a check for Android.
-dontwarn java.lang.ClassValue

# An annotation used for build tooling, won't be directly accessed.
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement
# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\606b4d93e4b147e72841e5146c5d0fa3\transformed\rules\lib\META-INF\com.android.tools\r8\coroutines.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\proguard.txt
-keep class * extends androidx.work.Worker
-keep class * extends androidx.work.InputMerger
# Keep all constructors on ListenableWorker, Worker
-keep public class * extends androidx.work.ListenableWorker {
    public <init>(...);
}
# We need to keep WorkerParameters for the ListenableWorker constructor
-keep class androidx.work.WorkerParameters

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\00ee8946583275229f58c64a5ecbb412\transformed\room-runtime-2.5.0\proguard.txt
-keep class * extends androidx.room.RoomDatabase
-dontwarn androidx.room.paging.**

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\00ee8946583275229f58c64a5ecbb412\transformed\room-runtime-2.5.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\5c275d7683859a49fefc7284389acf42\transformed\media-1.1.0\proguard.txt
# Copyright (C) 2017 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Prevent Parcelable objects from being removed or renamed.
-keep class android.support.v4.media.** implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}

# Prevent Parcelable objects from being removed or renamed.
-keep class androidx.media.** implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}
# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\5c275d7683859a49fefc7284389acf42\transformed\media-1.1.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\33a23a1921cc9e261ad760b2275ca606\transformed\jetified-play-services-base-18.0.1\proguard.txt
# b/35135904 Ensure that proguard will not strip the mResultGuardian.
-keepclassmembers class com.google.android.gms.common.api.internal.BasePendingResult {
  com.google.android.gms.common.api.internal.BasePendingResult$ReleasableResultGuardian mResultGuardian;
}



# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\33a23a1921cc9e261ad760b2275ca606\transformed\jetified-play-services-base-18.0.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\d11d375be189845fa6305707f685678c\transformed\jetified-play-services-tasks-18.1.0\proguard.txt


# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\d11d375be189845fa6305707f685678c\transformed\jetified-play-services-tasks-18.1.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\473c6549ebdad33ab68cbd325533c84f\transformed\jetified-play-services-basement-18.3.0\proguard.txt
# Needed when building against pre-Marshmallow SDK.
-dontwarn android.security.NetworkSecurityPolicy

# Needed when building against Marshmallow SDK.
-dontwarn android.app.Notification

# Protobuf has references not on the Android boot classpath
-dontwarn sun.misc.Unsafe
-dontwarn libcore.io.Memory

# Annotations used during internal SDK shrinking.
-dontwarn com.google.android.apps.common.proguard.UsedBy*
-dontwarn com.google.android.apps.common.proguard.SideEffectFree

# Annotations referenced by the SDK but whose definitions are contained in
# non-required dependencies.
-dontwarn javax.annotation.**
-dontwarn org.checkerframework.**
-dontwarn com.google.errorprone.annotations.**
-dontwarn org.jspecify.nullness.NullMarked

# Annotations no longer exist. Suppression prevents ProGuard failures in
# SDKs which depend on earlier versions of play-services-basement.
-dontwarn com.google.android.gms.common.util.VisibleForTesting

# Proguard flags for consumers of the Google Play services SDK
# https://developers.google.com/android/guides/setup#add_google_play_services_to_your_project

# Keep SafeParcelable NULL value, needed for reflection by DowngradeableSafeParcel
-keepclassmembers public class com.google.android.gms.common.internal.safeparcel.SafeParcelable {
    public static final *** NULL;
}

# Needed for Parcelable/SafeParcelable classes & their creators to not get renamed, as they are
# found via reflection.
-keep class com.google.android.gms.common.internal.ReflectedParcelable
-keepnames class * implements com.google.android.gms.common.internal.ReflectedParcelable
-keepclassmembers class * implements android.os.Parcelable {
  public static final *** CREATOR;
}

# Keep the classes/members we need for client functionality.
-keep @interface android.support.annotation.Keep
-keep @androidx.annotation.Keep class *
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <fields>;
}
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <methods>;
}

# Keep androidX equivalent of above android.support to allow Jetification.
-keep @interface androidx.annotation.Keep
-keep @androidx.annotation.Keep class *
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <fields>;
}
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <methods>;
}

# Keep the names of classes/members we need for client functionality.
-keep @interface com.google.android.gms.common.annotation.KeepName
-keepnames @com.google.android.gms.common.annotation.KeepName class *
-keepclassmembernames class * {
  @com.google.android.gms.common.annotation.KeepName *;
}

# Keep Dynamite API entry points
-keep @interface com.google.android.gms.common.util.DynamiteApi
-keep @com.google.android.gms.common.util.DynamiteApi public class * {
  public <fields>;
  public <methods>;
}



# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\473c6549ebdad33ab68cbd325533c84f\transformed\jetified-play-services-basement-18.3.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\proguard.txt
# Never inline methods, but allow shrinking and obfuscation.
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.view.ViewCompat$Api* {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.view.WindowInsetsCompat$*Impl* {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.app.NotificationCompat$*$Api*Impl {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.os.UserHandleCompat$Api*Impl {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.widget.EdgeEffectCompat$Api*Impl {
  <methods>;
}

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\4b00b460e00fe5ef6bdf95352ecf140f\transformed\rules\lib\META-INF\proguard\protobuf.pro
# Recently Protobuf Javalite introduced a change that relies on reflection,
# which doesn't work with Proguard. This rule keeps the reflection usages in
# (shaded) Protobuf classes in Tink as-is.
# The location of this file is determined by
# - https://developer.android.com/studio/build/shrink-code#configuration-files
# - https://docs.bazel.build/versions/master/be/java.html#java_library.resources
# See also:
# - https://github.com/google/tink/issues/361
# - https://github.com/protocolbuffers/protobuf/issues/6463
# WARNING: the shaded package name com.google.crypto.tink.shaded.protobuf must
# be kept in sync with jar_jar_rules.txt.
-keepclassmembers class * extends com.google.crypto.tink.shaded.protobuf.GeneratedMessageLite {
  <fields>;
}

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\4b00b460e00fe5ef6bdf95352ecf140f\transformed\rules\lib\META-INF\proguard\protobuf.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\f609bceb2779aea0408631e4886c8a03\transformed\rules-1.2.0\proguard.txt
# for 'can't find referenced method 'android.app.Instrumentation$ActivityResult execStartActivity' etc
-dontwarn androidx.test.runner.MonitoringInstrumentation

# for 'library class android.test.* extends or implements program class'
-dontwarn android.test.**

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\f609bceb2779aea0408631e4886c8a03\transformed\rules-1.2.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\b9446d4f8a39f1a78b33ccdd1b6fd8f3\transformed\espresso-core-3.2.0\proguard.txt
# Common proguard flags for all the espresso libraries.

# ignore 'cannot find java.beans and mockito integration' warnings
-dontwarn org.hamcrest.**

# ignore 'can't find referenced class sun.misc.Unsafe' from guava
-dontwarn androidx.test.espresso.core.deps.guava.**

# ignore 'can't find referenced class javax.lang.model.element.Modifier'
-dontwarn com.squareup.javawriter.JavaWriter

# ignore 'can't find referenced class sun.misc.Unsafe, libcore.io.Memory' from protobuf
-dontwarn androidx.test.espresso.core.deps.protobuf.**

# ignore 'can't find referenced method android.app.Instrumentation' from android.jar
-dontwarn androidx.test.runner.MonitoringInstrumentation

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\b9446d4f8a39f1a78b33ccdd1b6fd8f3\transformed\espresso-core-3.2.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\2f381f9c43e7c7a1f9f8899ba1e8df6e\transformed\runner-1.2.0\proguard.txt
# Proguard flags for the AndroidJUnitRunner library.

# avoid obfuscation of the instrumentation runners and orchestrator
-keepnames class androidx.test.**

# Annotation classes accessed via reflection
-keep class androidx.test.annotation.** { *; }

# for 'can't find referenced method 'android.app.Instrumentation$ActivityResult execStartActivity' etc
-dontwarn androidx.test.runner.MonitoringInstrumentation

# for 'library class android.test.* extends or implements program class'
-dontwarn android.test.**

# for 'can't find referenced class java.lang.management.RuntimeMXBean'
-dontwarn org.junit.rules.DisableOnDebug

# for 'can't find referenced class java.lang.management.ThreadMXBean'
-dontwarn org.junit.internal.runners.statements.FailOnTimeout

# for 'can't find referenced class java.beans.**, easymock, etc
-dontwarn org.hamcrest.**
# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\2f381f9c43e7c7a1f9f8899ba1e8df6e\transformed\runner-1.2.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\********************************\transformed\jetified-startup-runtime-1.0.0\proguard.txt
# This Proguard rule ensures that ComponentInitializers are are neither shrunk nor obfuscated.
# This is because they are discovered and instantiated during application initialization.
-keep class * extends androidx.startup.Initializer {
    # Keep the public no-argument constructor while allowing other methods to be optimized.
    <init>();
}

-assumenosideeffects class androidx.startup.StartupLogger

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\********************************\transformed\jetified-startup-runtime-1.0.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\5389d6eda3e5eba8ba922c509419fb7d\transformed\lifecycle-runtime-2.3.1\proguard.txt
-keepattributes *Annotation*

-keepclassmembers enum androidx.lifecycle.Lifecycle$Event {
    <fields>;
}

-keep !interface * implements androidx.lifecycle.LifecycleObserver {
}

-keep class * implements androidx.lifecycle.GeneratedAdapter {
    <init>(...);
}

-keepclassmembers class ** {
    @androidx.lifecycle.OnLifecycleEvent *;
}

# this rule is need to work properly when app is compiled with api 28, see b/142778206
# Also this rule prevents registerIn from being inlined.
-keepclassmembers class androidx.lifecycle.ReportFragment$LifecycleCallbacks { *; }
# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\5389d6eda3e5eba8ba922c509419fb7d\transformed\lifecycle-runtime-2.3.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\8c23a427f6bcf3e703744d727d438327\transformed\versionedparcelable-1.1.1\proguard.txt
-keep class * implements androidx.versionedparcelable.VersionedParcelable
-keep public class android.support.**Parcelizer { *; }
-keep public class androidx.**Parcelizer { *; }
-keep public class androidx.versionedparcelable.ParcelImpl

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\8c23a427f6bcf3e703744d727d438327\transformed\versionedparcelable-1.1.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\e504bdcb092e95d1061c167d203844f3\transformed\monitor-1.2.0\proguard.txt
# Proguard flags for the AndroidJUnitRunner library.

# avoid obfuscation of the instrumentation runners and orchestrator
-keepnames class androidx.test.**

# Annotation classes accessed via reflection
-keep class androidx.test.annotation.** { *; }

# for 'can't find referenced method 'android.app.Instrumentation$ActivityResult execStartActivity' etc
-dontwarn androidx.test.runner.MonitoringInstrumentation

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\e504bdcb092e95d1061c167d203844f3\transformed\monitor-1.2.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\02d4b24fbb4236fb3b924f02d99ca4fb\transformed\jetified-transport-backend-cct-3.1.8\proguard.txt
-dontwarn com.google.auto.value.AutoValue
-dontwarn com.google.auto.value.AutoValue$Builder

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\02d4b24fbb4236fb3b924f02d99ca4fb\transformed\jetified-transport-backend-cct-3.1.8\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\0930de29e8c1fb527a10523e48c2bd4b\transformed\jetified-transport-api-3.1.0\proguard.txt
-dontwarn com.google.auto.value.AutoValue
-dontwarn com.google.auto.value.AutoValue$Builder

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\0930de29e8c1fb527a10523e48c2bd4b\transformed\jetified-transport-api-3.1.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\a9d595898566c081a5ec89b7c346af69\transformed\jetified-firebase-encoders-json-18.0.0\proguard.txt

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\a9d595898566c081a5ec89b7c346af69\transformed\jetified-firebase-encoders-json-18.0.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\9bce9d2dbb7c8decf1dedf05887c7a70\transformed\jetified-firebase-components-17.1.5\proguard.txt
-dontwarn com.google.firebase.components.Component$Instantiation
-dontwarn com.google.firebase.components.Component$ComponentType

-keep class * implements com.google.firebase.components.ComponentRegistrar
-keep,allowshrinking interface com.google.firebase.components.ComponentRegistrar

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\9bce9d2dbb7c8decf1dedf05887c7a70\transformed\jetified-firebase-components-17.1.5\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\e2a1be298aba77a6ae8756a7d54c1db1\transformed\jetified-savedstate-1.0.0\proguard.txt
# Copyright (C) 2019 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

-keepclassmembers,allowobfuscation class * implements androidx.savedstate.SavedStateRegistry$AutoRecreated {
    <init>();
}

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\e2a1be298aba77a6ae8756a7d54c1db1\transformed\jetified-savedstate-1.0.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\60b048a3937f3ed8abf2c4d3ce5e63c0\transformed\lifecycle-viewmodel-2.1.0\proguard.txt
-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.ViewModel {
    <init>();
}

-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.AndroidViewModel {
    <init>(android.app.Application);
}

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\60b048a3937f3ed8abf2c4d3ce5e63c0\transformed\lifecycle-viewmodel-2.1.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\22dbe35869c0c450380ab4d162834db8\transformed\rules\lib\META-INF\proguard\androidx-annotations.pro
-keep,allowobfuscation @interface androidx.annotation.Keep
-keep @androidx.annotation.Keep class * {*;}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <methods>;
}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <fields>;
}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <init>(...);
}

-keepclassmembers,allowobfuscation class * {
  @androidx.annotation.DoNotInline <methods>;
}

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\22dbe35869c0c450380ab4d162834db8\transformed\rules\lib\META-INF\proguard\androidx-annotations.pro
# The proguard configuration file for the following section is <unknown>
-ignorewarnings
# End of content from <unknown>